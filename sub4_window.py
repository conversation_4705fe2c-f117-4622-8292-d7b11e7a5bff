# simple00_window.py
# --- النسخة الكاملة النهائية (مع محاولة إصلاح Parameter count mismatch) ---

import sys
import os
import sqlite3
import datetime
import importlib.util
import importlib
import pandas as pd  # لتصدير البيانات إلى Excel

def import_module_by_name(module_name):
    """استيراد وحدة بالاسم بشكل ديناميكي"""
    try:
        # محاولة استيراد الوحدة
        module = importlib.import_module(module_name)
        return module
    except ImportError as e:
        print(f"خطأ في استيراد الوحدة {module_name}: {str(e)}")
        return None

from PyQt5.QtGui import QPixmap, QFont, QIcon, QColor, QPalette, QFontMetrics, QCursor
from PyQt5.QtCore import Qt, QDate, QTime, QModelIndex, pyqtSlot, QSize, QItemSelection, QTimer
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QComboBox,
    QPushButton, QDateEdit, QTimeEdit, QTableView, QFrame,
    QGraphicsDropShadowEffect, QAbstractItemView, QHeaderView,
    QSizePolicy, QLabel, QStyledItemDelegate, QMessageBox, QCheckBox,
    QStyle, QStyleOptionButton, QDialog, QTextBrowser, QListWidget,
    QListWidgetItem, QLineEdit, QRadioButton, QButtonGroup, QFileDialog,
    QGroupBox, QGridLayout, QScrollArea, QTableWidget, QTableWidgetItem, QToolTip, QTextEdit
)

# استيراد وحدة رسائل التأكيد المخصصة
try:
    from sub100_window import ConfirmationDialogs
    CUSTOM_DIALOGS_IMPORTED = True
except ImportError:
    CUSTOM_DIALOGS_IMPORTED = False
    print("تعذر استيراد وحدة رسائل التأكيد المخصصة")
from PyQt5.QtSql import QSqlDatabase, QSqlQueryModel, QSqlQuery

def import_module_by_name(module_name):
    """استيراد وحدة برمجية من نفس المجلد الذي يتواجد فيه البرنامج الرئيسي"""
    # الحصول على المجلد الذي يتواجد فيه البرنامج الحالي
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # بناء المسار الكامل للملف المطلوب
    module_path = os.path.join(script_dir, f"{module_name}.py")

    if os.path.exists(module_path):
        try:
            # استيراد الوحدة البرمجية ديناميكيًا
            spec = importlib.util.spec_from_file_location(module_name, module_path)
            if spec:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                print(f"تم استيراد وحدة {module_name} بنجاح.")
                return module
        except Exception as e:
            print(f"خطأ في استيراد وحدة {module_name}: {e}")
            return None

    print(f"لم يتم العثور على وحدة {module_name}")
    return None

# استبدال طريقة استيراد StudentCardWindow
try:
    sub10_module = import_module_by_name("sub10_window")
    if sub10_module:
        StudentCardWindow = getattr(sub10_module, "StudentCardWindow", None)
        if StudentCardWindow:
            HIRASA_IMPORTED = True
        else:
            StudentCardWindow = None
            HIRASA_IMPORTED = False
    else:
        StudentCardWindow = None
        HIRASA_IMPORTED = False
except Exception:
    StudentCardWindow = None
    HIRASA_IMPORTED = False

# استيراد PrintListsWindow بنفس الطريقة النسبية
hirasa104_module = import_module_by_name("hirasa104_window")
if hirasa104_module and hasattr(hirasa104_module, "PrintListsWindow"):
    PrintListsWindow = hirasa104_module.PrintListsWindow
else:
    # تعريف صف بديل في حالة عدم توفر الأصلي
    class PrintListsWindow:
        def __init__(self, parent=None):
            # تم إزالة رسالة التحذير لتجنب ظهور نوافذ منبثقة
            pass
        def show(self):
            pass

# استيراد وحدة طباعة النماذج
def dummy_print(*args, **kwargs):
    return False

# استيراد وحدة طباعة النماذج
def dummy_print(*args, **kwargs):
    """دالة وهمية للطباعة ترجع False في حالة عدم توفر وحدة الطباعة"""
    return False

# تعيين دوال الطباعة إلى الدالة الوهمية
print_entry_form = print_doctor_visit_form = print_late_form = dummy_print
print_guidance_form = print_permission_form = print_secret_codes = dummy_print

# استيراد وحدة الطباعة الحرارية
print_entry_form_direct = print_late_form_direct = None

# استخدام تقنية تحويل النص إلى صورة للطباعة (thermal_image_print) كطريقة أساسية
try:
    thermal_image_print = import_module_by_name("thermal_image_print")
    if not thermal_image_print:
        raise ImportError("Could not import module 'thermal_image_print'")
    print_entry_form_direct = thermal_image_print.print_entry_form_direct
    print_late_form_direct = thermal_image_print.print_late_form_direct
    print("تم استيراد وحدة الطباعة الحرارية (تحويل النص إلى صورة) بنجاح")
except ImportError:
    thermal_image_print_module = import_module_by_name("thermal_image_print")
    if thermal_image_print_module:
        print_entry_form_direct = getattr(thermal_image_print_module, "print_entry_form_direct", None)
        print_late_form_direct = getattr(thermal_image_print_module, "print_late_form_direct", None)
        print("تم استيراد وحدة الطباعة الحرارية (تحويل النص إلى صورة) بنجاح")
    else:
        print("تحذير: لم يتم العثور على وحدة الطباعة الحرارية (thermal_image_print)")

# محاولة استيراد من print1.py (ملف زيارة الطبيب الجديد)
print1_module = import_module_by_name("print1")
if print1_module:
    print_doctor_visit_form_old = getattr(print1_module, "print_doctor_visit_form", print_doctor_visit_form)
else:
    # محاولة استيراد من doctor_visit_form (الملف القديم للتوافقية)
    doctor_visit_form_module = import_module_by_name("doctor_visit_form")
    if doctor_visit_form_module:
        print_doctor_visit_form_old = getattr(doctor_visit_form_module, "print_doctor_visit_form", print_doctor_visit_form)

# إذا لم يتم العثور على وحدة الطباعة الحرارية، عرض رسالة تحذير
if not print_entry_form_direct or not print_late_form_direct:
    print("تحذير: لم يتم العثور على وحدة الطباعة الحرارية")

    # عرض رسالة للمستخدم عند بدء تشغيل النافذة
    def show_printer_warning():
        try:
            # محاولة الحصول على قائمة الطابعات المتوفرة
            import win32print
            available_printers = [printer[2] for printer in win32print.EnumPrinters(2)]

            # إنشاء رسالة تحذير محسنة
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Warning)
            msg.setWindowTitle("تنبيه: الطابعة الحرارية غير محددة")
            msg.setText("لم يتم العثور على طابعة حرارية محددة في إعدادات البرنامج.")

            # إضافة معلومات مفصلة
            detailed_text = "يرجى تحديد الطابعة الحرارية في إعدادات البرنامج.\n\n"
            detailed_text += "الطابعات المتوفرة في النظام:\n"

            if available_printers:
                for i, printer in enumerate(available_printers, 1):
                    detailed_text += f"{i}. {printer}\n"
            else:
                detailed_text += "لا توجد طابعات متوفرة في النظام.\n"

            detailed_text += "\nيمكنك تحديد الطابعة الحرارية من خلال:\n"
            detailed_text += "1. فتح نافذة الإعدادات\n"
            detailed_text += "2. اختيار تبويب 'إعدادات الطابعة'\n"
            detailed_text += "3. تحديد الطابعة الحرارية من القائمة المنسدلة\n"
            detailed_text += "4. النقر على زر 'حفظ الإعدادات'"

            msg.setDetailedText(detailed_text)
            msg.setStandardButtons(QMessageBox.Ok)

            # تنسيق الرسالة
            msg.setStyleSheet("""
                QMessageBox {
                    font-family: Calibri;
                    font-size: 13pt;
                }
                QPushButton {
                    background-color: #0066cc;
                    color: white;
                    font-weight: bold;
                    border-radius: 5px;
                    padding: 8px 16px;
                    min-width: 100px;
                    min-height: 30px;
                    font-family: Calibri;
                    font-size: 13pt;
                }
                QPushButton:hover {
                    background-color: #0055bb;
                }
                QTextEdit {
                    font-family: Calibri;
                    font-size: 12pt;
                }
            """)

            # عرض الرسالة
            msg.exec_()
        except Exception as e:
            print(f"خطأ في عرض رسالة تحذير الطابعة: {e}")

    # تأخير عرض الرسالة لضمان تحميل النافذة أولاً
    QTimer.singleShot(1000, show_printer_warning)

# استيراد دالة طباعة نموذج زيارة الطبيب من الملف الثاني
print2_test_module = import_module_by_name("print2_test")
if print2_test_module and hasattr(print2_test_module, "print_doctor_visit_form"):
    print_doctor_visit_form_old = print2_test_module.print_doctor_visit_form

# تعريف فئة نافذة تصدير البيانات
class ExportWindow(QDialog):
    def __init__(self, parent=None, selected_students=None, db=None, academic_year=None):
        super().__init__(parent)
        self.parent = parent
        self.selected_students = selected_students or []
        self.db = db
        self.academic_year = academic_year
        self.selected_columns = []
        self.export_format = "excel"  # القيمة الافتراضية

        self.initUI()

    def initUI(self):
        # إعداد النافذة
        self.setWindowTitle("تصدير بيانات التلاميذ")
        self.setGeometry(100, 20, 800, 700)
        self.setMinimumSize(800, 700)
        self.setWindowIcon(QIcon("01.ico"))

        # تعيين نمط الخط
        font_title = QFont("Calibri", 14)
        font_title.setBold(True)
        font_details = QFont("Calibri", 13)
        font_details.setBold(True)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)

        # إنشاء إطار للعنوان
        title_frame = QFrame()
        title_frame.setFrameShape(QFrame.StyledPanel)
        title_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #0066cc;
                border-radius: 10px;
                background-color: #e6f2ff;
                padding: 10px;
                margin-bottom: 10px;
            }
        """)
        title_layout = QVBoxLayout(title_frame)
        title_layout.setSpacing(10)

        # عنوان النافذة
        title_label = QLabel("تصدير بيانات التلاميذ")
        title_label.setFont(font_title)
        title_label.setStyleSheet("color: #0066cc; font-weight: bold; font-size: 16pt;")
        title_label.setAlignment(Qt.AlignCenter)

        # معلومات عن عدد التلاميذ المحددين
        info_label = QLabel(f"عدد التلاميذ المحددين: {len(self.selected_students)}")
        info_label.setFont(font_details)
        info_label.setStyleSheet("color: #333333; font-weight: bold; font-size: 14pt;")
        info_label.setAlignment(Qt.AlignCenter)

        title_layout.addWidget(title_label)
        title_layout.addWidget(info_label)
        main_layout.addWidget(title_frame)

        # الأعمدة المراد تصديرها
        columns_group = QGroupBox("الأعمدة المراد تصديرها")
        columns_group.setFont(font_details)
        columns_group.setStyleSheet("""
            QGroupBox {
                border: 2px solid #0066cc;
                border-radius: 5px;
                margin-top: 15px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                color: #0066cc;
                font-weight: bold;
            }
        """)
        columns_layout = QVBoxLayout(columns_group)
        columns_layout.setSpacing(5)

        # إنشاء منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical, QScrollBar:horizontal {
                border: none;
                background: #f0f0f0;
                width: 10px;
                height: 10px;
                margin: 0px;
            }
            QScrollBar::handle:vertical, QScrollBar::handle:horizontal {
                background: #0066cc;
                min-height: 20px;
                min-width: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical,
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                border: none;
                background: none;
            }
        """)
        scroll_content = QWidget()
        scroll_layout = QGridLayout(scroll_content)
        scroll_layout.setSpacing(10)
        scroll_layout.setAlignment(Qt.AlignTop)

        # قائمة الأعمدة المتاحة - تم تعديلها لإزالة الأعمدة غير الموجودة في قاعدة البيانات
        available_columns = [
            {"name": "رت", "table": "اللوائح"},
            {"name": "الرمز", "table": "اللوائح"},
            {"name": "الاسم_والنسب", "table": "السجل_العام"},
            {"name": "القسم", "table": "اللوائح"},
            {"name": "المستوى", "table": "اللوائح"},
            {"name": "النوع", "table": "السجل_العام"},
            {"name": "تاريخ_الازدياد", "table": "السجل_العام"},
            {"name": "مكان_الازدياد", "table": "السجل_العام"},
            {"name": "الهاتف_الأول", "table": "السجل_العام"},
            {"name": "الهاتف_الثاني", "table": "السجل_العام"}
        ]

        # إنشاء مربعات اختيار للأعمدة بشكل أفقي
        self.column_checkboxes = {}

        # تحديد عدد الأعمدة في الشبكة
        columns_per_row = 3

        # التأكد من وجود عمود السنة الدراسية في قائمة الأعمدة المتاحة
        has_academic_year_column = any(col['name'] == "السنة_الدراسية" for col in available_columns)

        # إضافة عمود السنة الدراسية إذا لم يكن موجوداً
        if not has_academic_year_column:
            available_columns.append({"name": "السنة_الدراسية", "table": "اللوائح"})

        for i, column in enumerate(available_columns):
            checkbox = QCheckBox(column['name'])
            checkbox.setFont(font_details)
            checkbox.setStyleSheet("""
                QCheckBox {
                    padding: 8px;
                    font-weight: bold;
                    color: #333333;
                }
                QCheckBox:hover {
                    color: #0066cc;
                }
                QCheckBox::indicator {
                    width: 20px;
                    height: 20px;
                    border: 1px solid #999999;
                    border-radius: 3px;
                    background-color: #ffffff;
                }
                QCheckBox::indicator:checked {
                    background-color: #0066cc;
                    border: 1px solid #0066cc;
                    image: url(check.png);
                }
                QCheckBox::indicator:unchecked:hover {
                    border: 1px solid #0066cc;
                }
            """)

            # إضافة تلميح للمربع
            checkbox.setToolTip(f"اختر هذا العمود لتضمينه في ملف التصدير")

            # تحديد بعض الأعمدة افتراضياً
            default_columns = ["الرمز", "الاسم_والنسب", "القسم", "المستوى", "السنة_الدراسية"]
            if column['name'] in default_columns:
                checkbox.setChecked(True)

            # جعل عمود الرمز والسنة الدراسية إجباري ولا يمكن إلغاء تحديده
            if column['name'] in ["الرمز", "السنة_الدراسية"]:
                checkbox.setChecked(True)
                checkbox.setEnabled(False)
                checkbox.setToolTip("هذا العمود إجباري ولا يمكن إلغاء تحديده")

            self.column_checkboxes[column['name']] = {
                'checkbox': checkbox,
                'data': column
            }

            # إضافة المربع إلى التخطيط الشبكي
            row = i // columns_per_row
            col = i % columns_per_row
            scroll_layout.addWidget(checkbox, row, col)

        # إضافة زر تحديد الكل
        select_buttons_layout = QHBoxLayout()
        select_all_button = QPushButton("تحديد الكل")
        select_all_button.setFont(font_details)
        select_all_button.setStyleSheet("""
            QPushButton {
                background-color: #0066cc;
                color: white;
                font-weight: bold;
                border-radius: 5px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #0055bb;
            }
        """)
        select_all_button.setToolTip("تحديد جميع الأعمدة")
        select_all_button.clicked.connect(self.select_all_columns)

        select_buttons_layout.addWidget(select_all_button)

        # إضافة منطقة التمرير إلى التخطيط
        scroll_area.setWidget(scroll_content)
        columns_layout.addWidget(scroll_area)
        columns_layout.addLayout(select_buttons_layout)

        main_layout.addWidget(columns_group)

        # تعيين التنسيق الافتراضي إلى Excel
        self.export_format = "excel"

        # أزرار التصدير والإلغاء
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        self.export_button = QPushButton("تصدير")
        self.export_button.setFont(font_details)
        self.export_button.setToolTip("تصدير البيانات المحددة إلى ملف بالتنسيق المختار")
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                border-radius: 5px;
                padding: 8px 16px;
                min-width: 120px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #45a049;
                border: 2px solid #2e7d32;
            }
        """)
        self.export_button.setCursor(Qt.PointingHandCursor)
        self.export_button.clicked.connect(self.export_data)

        cancel_button = QPushButton("إلغاء")
        cancel_button.setFont(font_details)
        cancel_button.setToolTip("إلغاء عملية التصدير والعودة إلى النافذة السابقة")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                border-radius: 5px;
                padding: 8px 16px;
                min-width: 120px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #e53935;
                border: 2px solid #c62828;
            }
        """)
        cancel_button.setCursor(Qt.PointingHandCursor)
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addWidget(self.export_button)
        buttons_layout.addWidget(cancel_button)

        main_layout.addLayout(buttons_layout)



        # تطبيق النمط العام
        self.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #0066cc;
                border-radius: 10px;
            }
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                border: none;
                background: #f0f0f0;
                width: 10px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #0066cc;
                min-height: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
            QLabel {
                color: #333333;
            }
            QTableWidget {
                gridline-color: #cccccc;
                selection-background-color: #e6f2ff;
                selection-color: #000000;
            }
            QHeaderView::section {
                background-color: #0066cc;
                color: white;
                font-weight: bold;
                padding: 5px;
                border: 1px solid #0055bb;
            }
        """)

    def export_data(self):
        # جمع الأعمدة المحددة من مربعات الاختيار
        self.selected_columns = []
        for column_name, column_data in self.column_checkboxes.items():
            if column_data['checkbox'].isChecked():
                self.selected_columns.append(column_data['data'])

        if not self.selected_columns:
            # عرض رسالة تنبيه مع أيقونة البرنامج
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("تنبيه")
            msg_box.setIcon(QMessageBox.Warning)
            msg_box.setText("يرجى تحديد عمود واحد على الأقل للتصدير")
            msg_box.setWindowIcon(QIcon("01.ico"))

            # تعيين أحجام الأزرار وتنسيقها
            ok_button = msg_box.addButton(QMessageBox.Ok)
            ok_button.setText("موافق")
            ok_button.setStyleSheet("""
                QPushButton {
                    background-color: #f44336;
                    color: white;
                    font-weight: bold;
                    border-radius: 5px;
                    padding: 8px 16px;
                    min-width: 100px;
                    min-height: 30px;
                    font-family: Calibri;
                    font-size: 13pt;
                }
                QPushButton:hover {
                    background-color: #d32f2f;
                }
            """)

            msg_box.exec_()
            return

        if not self.selected_students:
            # عرض رسالة تنبيه مع أيقونة البرنامج
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("تنبيه")
            msg_box.setIcon(QMessageBox.Warning)
            msg_box.setText("لا يوجد تلاميذ محددين للتصدير")
            msg_box.setWindowIcon(QIcon("01.ico"))

            # تعيين أحجام الأزرار وتنسيقها
            ok_button = msg_box.addButton(QMessageBox.Ok)
            ok_button.setText("موافق")
            ok_button.setStyleSheet("""
                QPushButton {
                    background-color: #f44336;
                    color: white;
                    font-weight: bold;
                    border-radius: 5px;
                    padding: 8px 16px;
                    min-width: 100px;
                    min-height: 30px;
                    font-family: Calibri;
                    font-size: 13pt;
                }
                QPushButton:hover {
                    background-color: #d32f2f;
                }
            """)

            msg_box.exec_()
            return

        # التحقق من وجود بيانات للتصدير
        data = self.get_export_data()
        if not data:
            # عرض رسالة تنبيه مع أيقونة البرنامج
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("تنبيه")
            msg_box.setIcon(QMessageBox.Warning)
            msg_box.setText("لا توجد بيانات للتصدير. يرجى التأكد من اختيار التلاميذ والأعمدة بشكل صحيح.")
            msg_box.setWindowIcon(QIcon("01.ico"))

            # تعيين أحجام الأزرار وتنسيقها
            ok_button = msg_box.addButton(QMessageBox.Ok)
            ok_button.setText("موافق")
            ok_button.setStyleSheet("""
                QPushButton {
                    background-color: #f44336;
                    color: white;
                    font-weight: bold;
                    border-radius: 5px;
                    padding: 8px 16px;
                    min-width: 100px;
                    min-height: 30px;
                    font-family: Calibri;
                    font-size: 13pt;
                }
                QPushButton:hover {
                    background-color: #d32f2f;
                }
            """)

            msg_box.exec_()
            return

        # إنشاء مجلد رئيسي على سطح المكتب
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        main_reports_dir = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
        os.makedirs(main_reports_dir, exist_ok=True)

        # إنشاء مجلد تصدير داخل المجلد الرئيسي
        export_dir = os.path.join(main_reports_dir, "تصدير")
        os.makedirs(export_dir, exist_ok=True)

        # تحديد اسم الملف الافتراضي
        default_filename = f"تصدير_بيانات_التلاميذ_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"

        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ ملف Excel",
            os.path.join(export_dir, default_filename + ".xlsx"),
            "Excel Files (*.xlsx)"
        )
        if file_path:
            self.export_to_excel(file_path)

    def export_to_excel(self, file_path):
        try:
            # جمع البيانات من قاعدة البيانات
            data = self.get_export_data()

            # التحقق من وجود بيانات للتصدير
            if not data:
                raise ValueError("لا توجد بيانات للتصدير. يرجى التأكد من اختيار التلاميذ والأعمدة بشكل صحيح.")

            # إنشاء DataFrame
            df = pd.DataFrame(data)

            # التحقق من وجود أعمدة في البيانات
            if df.empty or len(df.columns) == 0:
                raise ValueError("لا توجد أعمدة في البيانات المراد تصديرها.")

            # إنشاء ملف Excel
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # تصدير البيانات بدون فهرس - بدء من الصف الثامن لإضافة الشعار واسم المؤسسة والعنوان والسنة الدراسية والعنوان والملاحظات
                df.to_excel(writer, index=False, sheet_name='بيانات التلاميذ', startrow=7)

                # تنسيق الورقة
                worksheet = writer.sheets['بيانات التلاميذ']

                # الحصول على بيانات المؤسسة من قاعدة البيانات
                from openpyxl.drawing.image import Image
                from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

                # استعلام للحصول على بيانات المؤسسة
                query = QSqlQuery(db=self.db)
                school_name = "المؤسسة التعليمية"
                academic_year = self.academic_year
                logo_path = ""
                school_address = ""

                # التحقق من اتصال قاعدة البيانات
                # sqlite3.Connection لا يحتاج فتح/إغلاق صريح
                if not self.db:
                    print("خطأ: قاعدة البيانات غير متصلة")
                    return

                # تنفيذ الاستعلام للحصول على بيانات المؤسسة
                sql_query = "SELECT المؤسسة, السنة_الدراسية, ImagePath1, البلدة FROM بيانات_المؤسسة LIMIT 1"

                if query.exec_(sql_query) and query.next():
                    school_name = query.value(0) or school_name
                    academic_year = query.value(1) or academic_year
                    logo_path = query.value(2) or ""
                    school_address = query.value(3) or ""

                # إضافة شعار المؤسسة (إذا كان متوفرًا) في وسط الصفحة
                try:
                    logo_file = None
                    if logo_path and os.path.exists(logo_path):
                        logo_file = logo_path
                    elif os.path.exists("logo.png"):
                        logo_file = "logo.png"

                    if logo_file:
                        logo = Image(logo_file)
                        # تعيين حجم الشعار
                        logo.width = 200
                        logo.height = 100
                        # تحديد عمود الوسط
                        middle_col_index = len(df.columns) // 2
                        if middle_col_index > 0:
                            middle_col_index -= 1  # تعديل لجعل الشعار في المنتصف تمامًا
                        middle_col_letter = chr(65 + middle_col_index)
                        worksheet.add_image(logo, f"{middle_col_letter}1")
                except Exception:
                    pass  # تجاهل أي أخطاء في إضافة الشعار

                # إضافة بيانات المؤسسة تحت الشعار

                # تعريف نمط الخط والمحاذاة المشتركة
                center_alignment = Alignment(horizontal='center', vertical='center')

                # إضافة اسم المؤسسة في الصف الثالث (تحت الشعار)
                cell = worksheet.cell(row=3, column=1)
                cell.value = school_name
                cell.font = Font(name='Calibri', size=14, bold=True, color="0000FF")
                cell.alignment = center_alignment

                # دمج خلايا اسم المؤسسة في جميع الأعمدة
                if len(df.columns) > 1:
                    worksheet.merge_cells(start_row=3, start_column=1, end_row=3, end_column=len(df.columns))

                # إضافة عنوان المؤسسة في الصف الرابع
                if school_address:
                    cell = worksheet.cell(row=4, column=1)
                    cell.value = school_address
                    cell.font = Font(name='Calibri', size=13, bold=True)
                    cell.alignment = center_alignment

                    # دمج خلايا عنوان المؤسسة في جميع الأعمدة
                    if len(df.columns) > 1:
                        worksheet.merge_cells(start_row=4, start_column=1, end_row=4, end_column=len(df.columns))

                # إضافة السنة الدراسية في الصف الخامس
                cell = worksheet.cell(row=5, column=1)
                cell.value = f"السنة الدراسية: {academic_year}"
                cell.font = Font(name='Calibri', size=13, bold=True, color="0000FF")
                cell.alignment = center_alignment

                # دمج خلايا السنة الدراسية في جميع الأعمدة
                if len(df.columns) > 1:
                    worksheet.merge_cells(start_row=5, start_column=1, end_row=5, end_column=len(df.columns))

                # تنسيق رأس الجدول
                header_fill = PatternFill(start_color="0070C0", end_color="0070C0", fill_type="solid")
                header_font = Font(name='Calibri', size=13, bold=True, color="FFFFFF")
                header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

                # تنسيق حدود الخلايا
                thin_border = Border(
                    left=Side(style='thin', color="000000"),
                    right=Side(style='thin', color="000000"),
                    top=Side(style='thin', color="000000"),
                    bottom=Side(style='thin', color="000000")
                )

                # التحقق من وجود أعمدة قبل تطبيق التنسيق
                if len(df.columns) > 0:
                    # تطبيق التنسيق على رأس الجدول
                    for col_idx, column in enumerate(df.columns, 1):
                        cell = worksheet.cell(row=8, column=col_idx)
                        cell.fill = header_fill
                        cell.font = header_font
                        cell.alignment = header_alignment
                        cell.border = thin_border

                        # ضبط عرض العمود حسب محتواه
                        try:
                            column_width = max(len(str(column)), df[column].astype(str).map(len).max())
                            worksheet.column_dimensions[cell.column_letter].width = min(max(column_width + 2, 10), 30)
                        except Exception as col_error:
                            print(f"خطأ في ضبط عرض العمود {column}: {col_error}")
                            worksheet.column_dimensions[cell.column_letter].width = 15  # عرض افتراضي

                    # تنسيق خلايا البيانات
                    data_font = Font(name='Calibri', size=13)
                    data_alignment = Alignment(horizontal='center', vertical='center')

                    # تطبيق التنسيق على خلايا البيانات
                    for row_idx in range(9, 9 + len(df)):
                        # تعيين ارتفاع الصف
                        worksheet.row_dimensions[row_idx].height = 35

                        for col_idx in range(1, len(df.columns) + 1):
                            cell = worksheet.cell(row=row_idx, column=col_idx)
                            cell.font = data_font
                            cell.alignment = data_alignment
                            cell.border = thin_border

                    # تعيين ارتفاع صف الرأس
                    worksheet.row_dimensions[8].height = 40

            # فتح الملف بعد التصدير
            try:
                if os.path.exists(file_path):
                    if os.name == 'nt':  # نظام ويندوز
                        os.startfile(file_path)
                    else:  # نظام لينكس أو ماك
                        import subprocess
                        subprocess.Popen(['xdg-open', file_path])
            except Exception as e:
                print(f"خطأ في فتح الملف: {e}")

            # عرض رسالة نجاح باستخدام الدالة المنسقة
            self.show_styled_message_box(
                title="نجاح",
                message=f"تم تصدير البيانات بنجاح إلى:\n{file_path}\nوتم فتح الملف تلقائياً",
                icon=QMessageBox.Information,
                button_color="#4CAF50",
                button_hover_color="#45a049"
            )
            self.accept()
        except Exception as e:
            # عرض رسالة خطأ باستخدام الدالة المنسقة
            self.show_styled_message_box(
                title="خطأ",
                message=f"حدث خطأ أثناء تصدير البيانات:\n{str(e)}",
                icon=QMessageBox.Critical,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )

    def show_styled_message_box(self, title, message, icon=QMessageBox.Information, button_color="#4CAF50", button_hover_color="#45a049"):
        """عرض رسالة منسقة"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setIcon(icon)
        msg_box.setText(message)
        msg_box.setWindowIcon(QIcon("01.ico"))

        # تعيين أحجام الأزرار وتنسيقها
        ok_button = msg_box.addButton(QMessageBox.Ok)
        ok_button.setText("موافق")
        ok_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {button_color};
                color: white;
                font-weight: bold;
                border-radius: 5px;
                padding: 8px 16px;
                min-width: 100px;
                min-height: 30px;
                font-family: Calibri;
                font-size: 13pt;
            }}
            QPushButton:hover {{
                background-color: {button_hover_color};
            }}
        """)

        msg_box.exec_()

    def select_all_columns(self):
        """تحديد جميع الأعمدة"""
        for column_data in self.column_checkboxes.values():
            column_data['checkbox'].setChecked(True)

    def deselect_all_columns(self):
        """إلغاء تحديد جميع الأعمدة"""
        for column_data in self.column_checkboxes.values():
            column_data['checkbox'].setChecked(False)

    def get_export_data(self):
        """جمع البيانات من قاعدة البيانات للتصدير"""
        data = []

        # التحقق من وجود اتصال بقاعدة البيانات وتلاميذ محددين وأعمدة محددة
        if not self.db or not self.selected_students or not self.selected_columns:
            return data

        try:
            # بناء استعلام SQL
            select_columns = []
            tables = set()

            for column in self.selected_columns:
                table_name = column['table']
                column_name = column['name']
                tables.add(table_name)
                select_columns.append(f"{table_name}.{column_name}")

            # بناء قائمة الرموز للاستعلام
            codes_list = ", ".join([f"'{code}'" for code in self.selected_students])
            if not codes_list:
                return data

            # بناء استعلام SQL مع ترتيب حسب المستوى ثم القسم ثم رت
            query_str = f"""
                SELECT {', '.join(select_columns)}
                FROM اللوائح
                JOIN السجل_العام ON اللوائح.الرمز = السجل_العام.الرمز
                WHERE اللوائح.الرمز IN ({codes_list})
                AND اللوائح.السنة_الدراسية = '{self.academic_year}'
                ORDER BY اللوائح.المستوى, اللوائح.القسم, اللوائح.رت
            """

            query = QSqlQuery(db=self.db)
            if query.exec_(query_str):
                while query.next():
                    row_data = {}
                    for i, column in enumerate(self.selected_columns):
                        row_data[column['name']] = query.value(i)
                    data.append(row_data)
        except Exception:
            pass

        return data

# تعديل دالة البحث عن قاعدة البيانات
def create_connection():
    # البحث عن قاعدة البيانات في مجلد البرنامج الحالي
    script_dir = os.path.dirname(os.path.abspath(__file__))
    db_name = "data.db"
    db_path = os.path.join(script_dir, db_name)
    connection_name = "SimpleSearchDBConnection"

    # التحقق ما إذا كان الاتصال موجودًا مسبقًا
    if QSqlDatabase.contains(connection_name):
        db = QSqlDatabase.database(connection_name)
    else:
        db = QSqlDatabase.addDatabase("QSQLITE", connection_name)

    db.setDatabaseName(db_path)

    if not os.path.exists(db_path):
         return False, None, f"Database file not found at '{db_path}'"
    if not db.open():
       error_msg = f"Error: Unable to connect to database '{db_path}'.\n{db.lastError().text()}"
       return False, None, error_msg
    return True, db, None

def get_current_academic_year(db):
    if not db: return None, "Database not connected"
    query = QSqlQuery(db=db)
    if query.exec_("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1"):
        if query.next(): year = query.value(0); return year, None
        else: return None, "No academic year found in 'بيانات_المؤسسة'."
    else: error_msg = f"Error querying academic year: {query.lastError().text()}"; return None, error_msg

class BlueTextDelegate(QStyledItemDelegate):
    def initStyleOption(self, option, index):
        super().initStyleOption(option, index)
        if index.column() == 1: # عمود الرمز
             option.palette.setColor(QPalette.Text, QColor(0, 0, 255))  # لون أزرق

    def createEditor(self, parent, option, index):
        return None

    def editorEvent(self, event, model, option, index):
        # إضافة تلميح عند التحويم فوق عمود الرمز
        if event.type() == event.Enter:
            QToolTip.showText(event.globalPos(), "انقر على الرمز لنسخه إلى الحافظة", None, option.rect, 5000)
        return super().editorEvent(event, model, option, index)

class CheckBoxDelegate(QStyledItemDelegate):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.checked_items = set()  # لتخزين حالة مربعات الاختيار
        self.parent_window = None  # مرجع للنافذة الرئيسية

    def set_parent_window(self, window):
        """تعيين مرجع للنافذة الرئيسية"""
        self.parent_window = window

    def createEditor(self, parent, option, index):
        return None

    def paint(self, painter, option, index):
        # إنشاء مربع اختيار محسن مع تأثيرات بصرية متقدمة
        opt = QStyleOptionButton()
        opt.rect = option.rect
        opt.state |= QStyle.State_Enabled

        # تحديد حالة مربع الاختيار
        row_key = index.row()
        is_checked = row_key in self.checked_items

        if is_checked:
            opt.state |= QStyle.State_On
        else:
            opt.state |= QStyle.State_Off

        # تطبيق تأثيرات لونية متقدمة للتحديد
        if option.state & QStyle.State_Selected:
            # خلفية صفراء ذهبية للصف المحدد
            painter.fillRect(option.rect, QColor("#ffc107"))
        elif is_checked:
            # خلفية صفراء فاتحة للعنصر المحدد
            painter.fillRect(option.rect, QColor("#fff3cd"))
        else:
            # خلفية عادية مع تأثير hover
            if option.state & QStyle.State_MouseOver:
                painter.fillRect(option.rect, QColor("#e3f2fd"))

        # رسم مربع الاختيار المحسن في منتصف الخلية
        # حساب حجم مناسب للمربع بناءً على ارتفاع الصف
        row_height = option.rect.height()
        checkbox_size = min(22, row_height - 6)  # حجم مناسب مع هامش 3 بكسل من كل جهة

        checkbox_rect = QStyle.alignedRect(
            Qt.RightToLeft,
            Qt.AlignCenter,
            QSize(checkbox_size, checkbox_size),
            option.rect
        )
        opt.rect = checkbox_rect

        # تطبيق ألوان مخصصة للمربع
        if is_checked:
            # رسم مربع محدد بلون أصفر ذهبي
            painter.fillRect(checkbox_rect, QColor("#ffc107"))
            painter.setPen(QColor("#ff8f00"))
            painter.drawRect(checkbox_rect)

            # رسم علامة الصح بحجم مناسب
            painter.setPen(QColor("#0d47a1"))
            font_size = max(10, checkbox_size - 6)  # حجم خط مناسب للمربع
            painter.setFont(QFont("Arial", font_size, QFont.Bold))
            painter.drawText(checkbox_rect, Qt.AlignCenter, "✓")
        else:
            # رسم مربع غير محدد
            painter.fillRect(checkbox_rect, QColor("white"))
            painter.setPen(QColor("#bdc3c7"))
            painter.drawRect(checkbox_rect)

    def editorEvent(self, event, model, option, index):
        # معالجة أحداث النقر على مربع الاختيار مع تأثيرات بصرية متقدمة
        _ = option  # استخدام المتغير لتجنب التحذيرات

        if event.type() == event.MouseButtonRelease:
            row_key = index.row()

            # الحصول على رمز التلميذ من الصف الحالي
            if self.parent_window and model:
                code_index = model.index(row_key, 2)  # الرمز في العمود 2
                code = model.data(code_index, Qt.DisplayRole)

                if row_key in self.checked_items:
                    # إزالة الصف من قائمة الصفوف المحددة
                    self.checked_items.remove(row_key)

                    # تطبيق تأثير إزالة التحديد
                    self.apply_row_deselection_effect(model, row_key)

                    # إزالة رمز التلميذ من قائمة التلاميذ المحددين في النافذة الرئيسية
                    if code and hasattr(self.parent_window, 'all_selected_students'):
                        if code in self.parent_window.all_selected_students:
                            self.parent_window.all_selected_students.remove(code)
                else:
                    # إضافة الصف إلى قائمة الصفوف المحددة
                    self.checked_items.add(row_key)

                    # تطبيق تأثير التحديد المتقدم
                    self.apply_row_selection_effect(model, row_key)

                    # إضافة رمز التلميذ إلى قائمة التلاميذ المحددين في النافذة الرئيسية
                    if code and hasattr(self.parent_window, 'all_selected_students'):
                        self.parent_window.all_selected_students.add(code)

            # إجبار إعادة رسم الصف كاملاً للتأثيرات البصرية
            for col in range(model.columnCount()):
                cell_index = model.index(row_key, col)
                model.dataChanged.emit(cell_index, cell_index)

            return True
        return False

    def apply_row_selection_effect(self, model, row):
        """تطبيق تأثير التحديد المتقدم على الصف"""
        try:
            # تطبيق ألوان التحديد المتقدمة على جميع خلايا الصف
            for col in range(model.columnCount()):
                index = model.index(row, col)
                # سيتم تطبيق الألوان في دالة paint
                model.dataChanged.emit(index, index)
        except Exception as e:
            print(f"خطأ في تطبيق تأثير التحديد: {e}")

    def apply_row_deselection_effect(self, model, row):
        """تطبيق تأثير إزالة التحديد على الصف"""
        try:
            # إزالة ألوان التحديد من جميع خلايا الصف
            for col in range(model.columnCount()):
                index = model.index(row, col)
                # سيتم إزالة الألوان في دالة paint
                model.dataChanged.emit(index, index)
        except Exception as e:
            print(f"خطأ في تطبيق تأثير إزالة التحديد: {e}")

    def get_checked_rows(self):
        return list(self.checked_items)

class Sub4Window(QWidget):
    def __init__(self, db, academic_year, parent=None):
        super().__init__(parent)
        self.db = db
        self.current_academic_year = academic_year
        self.selected_level = None
        self.selected_class = None
        self.code_column_index = 1 # Assuming column 1 is 'الرمز'
        self.checkbox_column_index = 0  # إضافة مؤشر لعمود مربع الاختيار

        # قائمة لتخزين رموز التلاميذ المحددين من جميع الأقسام
        self.all_selected_students = set()

        self.font_calibri_13_bold = QFont('Calibri', 13); self.font_calibri_13_bold.setBold(True)
        self.font_calibri_13_normal = QFont('Calibri', 13)
        self.font_calibri_12_normal = QFont('Calibri', 12)

        self.initUI()
        self.apply_styles()

        # تطبيق التحسينات المتقدمة من sub252_window.py
        self.setup_responsive_layout()
        self.apply_advanced_visual_effects()
        self.enhance_table_appearance()
        self.enhance_checkbox_appearance()
        self.add_button_animations()
        self.apply_beautiful_color_scheme()

        self.connect_signals()
        self.load_initial_data()
        self.checkbox_delegate = CheckBoxDelegate(self.table_lists)
        # تعيين مرجع النافذة الرئيسية في مندوب مربع الاختيار
        self.checkbox_delegate.set_parent_window(self)

        # ربط إشارة التحديث من النافذة الرئيسية إذا كانت موجودة
        if parent and hasattr(parent, 'sub2_window_instance'):
            if hasattr(parent.sub2_window_instance, 'data_updated_signal'):
                parent.sub2_window_instance.data_updated_signal.connect(self.refresh_data)

    def create_action_button(self, text, color):
        """إنشاء زر عمليات منسق مع عرض حسب النص - نظام متطور من sub252_window.py"""
        button = QPushButton(text)
        button.setFont(QFont("Calibri", 13, QFont.Bold))
        button.setMaximumHeight(43)

        # حساب العرض المناسب حسب النص مع زيادة مناسبة
        font_metrics = QFontMetrics(button.font())
        text_width = font_metrics.width(text)
        # زيادة مناسبة للحواف والمساحة (30 بكسل كحد أدنى)
        padding = max(30, len(text) * 2)  # زيادة تتناسب مع طول النص
        button_width = text_width + padding
        button.setMinimumWidth(button_width)
        button.setMaximumWidth(button_width + 10)  # مرونة قليلة

        # تحويل لون hex إلى لون مظلم
        dark_color = self.darken_color(color, 50)
        light_color = self.lighten_color(color, 40)

        button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color},
                    stop: 1 {dark_color}
                );
                color: white;
                border: 2px solid {dark_color};
                border-radius: 8px;
                padding: 8px 15px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {light_color},
                    stop: 1 {color}
                );
                border: 2px solid {color};
            }}
            QPushButton:pressed {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {dark_color},
                    stop: 1 {self.darken_color(dark_color, 20)}
                );
                border: 2px solid {self.darken_color(dark_color, 20)};
            }}
        """)
        return button

    def create_dropdown_button(self, layout):
        """إنشاء زر بقائمة منسدلة للأوراق والتصدير"""
        from PyQt5.QtWidgets import QMenu, QAction

        # إنشاء الزر الرئيسي
        dropdown_button = self.create_action_button("الأوراق والتصدير", "#8e44ad")
        dropdown_button.setCursor(Qt.PointingHandCursor)

        # إنشاء القائمة المنسدلة
        menu = QMenu(dropdown_button)
        menu.setLayoutDirection(Qt.RightToLeft)

        # تنسيق القائمة المنسدلة
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 2px solid #8e44ad;
                border-radius: 8px;
                padding: 5px;
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
            }
            QMenu::item {
                background-color: transparent;
                padding: 8px 20px;
                margin: 2px;
                border-radius: 5px;
                color: #2c3e50;
            }
            QMenu::item:selected {
                background-color: #8e44ad;
                color: white;
            }
            QMenu::item:pressed {
                background-color: #7d3c98;
                color: white;
            }
        """)

        # إضافة العناصر للقائمة
        menu_items = [
            ("بطاقة تلميذ", "👤", self.show_student_data),
            ("ورقة توجيه", "📋", self.print_student_card),
            ("ورقة استئذان", "📝", self.show_violations_permission_slip),
            ("زيارة الطبيب", "🏥", self.print_doctor_visit),
            ("تصدير البيانات", "📊", self.export_selected_students),
            ("تعليمات", "❓", self.show_help)
        ]

        for item_text, icon, action in menu_items:
            action_item = QAction(f"{icon} {item_text}", menu)
            action_item.triggered.connect(action)
            menu.addAction(action_item)

        # ربط القائمة بالزر
        dropdown_button.setMenu(menu)

        # إضافة الزر للتخطيط
        layout.addWidget(dropdown_button)

        # حفظ مرجع للزر
        self.buttons["الأوراق والتصدير"] = dropdown_button

    def create_student_card_button(self, layout):
        """إنشاء زر بطاقة تلميذ مع قائمة منسدلة"""
        from PyQt5.QtWidgets import QMenu, QAction

        # إنشاء الزر الرئيسي
        student_card_button = self.create_action_button("بطاقة تلميذ", "#e67e22")
        student_card_button.setCursor(Qt.PointingHandCursor)
        
        # مضاعفة عرض زر بطاقة التلميذ
        current_width = student_card_button.minimumWidth()
        doubled_width = current_width * 2
        student_card_button.setMinimumWidth(doubled_width)
        student_card_button.setMaximumWidth(doubled_width)

        # إنشاء القائمة المنسدلة
        menu = QMenu(student_card_button)
        menu.setLayoutDirection(Qt.RightToLeft)

        # تنسيق القائمة المنسدلة
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 2px solid #e67e22;
                border-radius: 8px;
                padding: 5px;
                font-family: Calibri;
                font-size: 13pt;
                font-weight: bold;
            }
            QMenu::item {
                background-color: transparent;
                padding: 8px 20px;
                margin: 2px;
                border-radius: 5px;
                color: #2c3e50;
            }
            QMenu::item:selected {
                background-color: #e67e22;
                color: white;
            }
            QMenu::item:pressed {
                background-color: #d35400;
                color: white;
            }
        """)

        # إضافة العناصر للقائمة
        menu_items = [
            ("بيانات التلميذ", "👤", self.show_student_data),
            ("مسك تبرير الغياب", "📝", self.show_absence_justification),
            ("مسك المخالفات", "⚠️", self.open_violations_window)
        ]

        for item_text, icon, action in menu_items:
            action_item = QAction(f"{icon} {item_text}", menu)
            action_item.triggered.connect(action)
            menu.addAction(action_item)

        # ربط القائمة بالزر
        student_card_button.setMenu(menu)

        # إضافة الزر للتخطيط
        layout.addWidget(student_card_button)

        # حفظ مرجع للزر
        self.buttons["بطاقة تلميذ"] = student_card_button

    def lighten_color(self, color, amount):
        """تفتيح اللون - دالة متقدمة من sub252_window.py"""
        if color.startswith('#'):
            color = color[1:]

        r = min(255, int(color[0:2], 16) + amount)
        g = min(255, int(color[2:4], 16) + amount)
        b = min(255, int(color[4:6], 16) + amount)

        return f"#{r:02x}{g:02x}{b:02x}"

    def darken_color(self, color, amount):
        """تغميق اللون - دالة متقدمة من sub252_window.py"""
        if color.startswith('#'):
            color = color[1:]

        r = max(0, int(color[0:2], 16) - amount)
        g = max(0, int(color[2:4], 16) - amount)
        b = max(0, int(color[4:6], 16) - amount)

        return f"#{r:02x}{g:02x}{b:02x}"

    def show_enhanced_message(self, message_type, title, message, details=None):
        """عرض رسالة محسنة ومميزة - نظام متطور من sub252_window.py"""
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)

        # تحديد نوع الرسالة والأيقونة
        if message_type == "success":
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #e8f5e8;
                    border: 2px solid #4caf50;
                    border-radius: 10px;
                }
                QMessageBox QLabel {
                    color: #2e7d32;
                    font-family: 'Calibri';
                    font-size: 14px;
                    font-weight: bold;
                }
                QMessageBox QPushButton {
                    background-color: #4caf50;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-family: 'Calibri';
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #45a049;
                }
            """)
        elif message_type == "warning":
            msg_box.setIcon(QMessageBox.Warning)
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #fff3e0;
                    border: 2px solid #ff9800;
                    border-radius: 10px;
                }
                QMessageBox QLabel {
                    color: #e65100;
                    font-family: 'Calibri';
                    font-size: 14px;
                    font-weight: bold;
                }
                QMessageBox QPushButton {
                    background-color: #ff9800;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-family: 'Calibri';
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #f57c00;
                }
            """)
        elif message_type == "error":
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #ffebee;
                    border: 2px solid #f44336;
                    border-radius: 10px;
                }
                QMessageBox QLabel {
                    color: #c62828;
                    font-family: 'Calibri';
                    font-size: 14px;
                    font-weight: bold;
                }
                QMessageBox QPushButton {
                    background-color: #f44336;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-family: 'Calibri';
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #d32f2f;
                }
            """)
        elif message_type == "info":
            msg_box.setIcon(QMessageBox.Information)
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #e3f2fd;
                    border: 2px solid #2196f3;
                    border-radius: 10px;
                }
                QMessageBox QLabel {
                    color: #1565c0;
                    font-family: 'Calibri';
                    font-size: 14px;
                    font-weight: bold;
                }
                QMessageBox QPushButton {
                    background-color: #2196f3;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px 16px;
                    font-family: 'Calibri';
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #1976d2;
                }
            """)

        if details:
            msg_box.setDetailedText(details)

        return msg_box.exec_()

    def show_enhanced_question(self, title, message, details=None):
        """عرض سؤال محسن ومميز - نظام متطور من sub252_window.py"""
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Question)
        msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg_box.setDefaultButton(QMessageBox.No)

        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: #f3e5f5;
                border: 2px solid #9c27b0;
                border-radius: 10px;
            }
            QMessageBox QLabel {
                color: #6a1b9a;
                font-family: 'Calibri';
                font-size: 14px;
                font-weight: bold;
            }
            QMessageBox QPushButton {
                background-color: #9c27b0;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-family: 'Calibri';
                font-size: 12px;
                font-weight: bold;
                min-width: 80px;
                margin: 2px;
            }
            QMessageBox QPushButton:hover {
                background-color: #8e24aa;
            }
        """)

        if details:
            msg_box.setDetailedText(details)

        return msg_box.exec_()

    def setup_responsive_layout(self):
        """إعداد التخطيط المتجاوب - نظام متطور من sub252_window.py"""
        # تحسين أحجام الأعمدة تلقائياً
        if hasattr(self, 'table_lists'):
            header = self.table_lists.horizontalHeader()

            # تعيين أحجام الأعمدة بشكل متجاوب
            total_width = self.table_lists.width()

            # توزيع العرض بنسب مناسبة
            column_ratios = [0.08, 0.12, 0.15, 0.35, 0.15, 0.15]  # نسب الأعمدة

            for i, ratio in enumerate(column_ratios):
                if i < self.table_lists.model().columnCount():
                    column_width = int(total_width * ratio)
                    self.table_lists.setColumnWidth(i, column_width)

            # تمكين التمدد للعمود الأخير
            header.setStretchLastSection(True)

            # تحسين ارتفاع الصفوف - مناسب لمربعات الاختيار
            self.table_lists.verticalHeader().setDefaultSectionSize(30)

    def apply_advanced_visual_effects(self):
        """تطبيق التأثيرات البصرية المتقدمة - محسن للتوافق مع PyQt5"""
        # تحسين مظهر النافذة الرئيسية
        self.setStyleSheet(self.styleSheet() + """
            /* تأثيرات الحدود المتقدمة */
            QWidget#centralWidget {
                border-radius: 15px;
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fc,
                    stop: 0.5 #e9ecef,
                    stop: 1 #dee2e6
                );
            }

            /* تحسين مظهر الجداول */
            QTableView {
                border: 3px solid #3498db;
                border-radius: 12px;
                background-color: white;
                gridline-color: #bdc3c7;
                font-size: 14pt;
                font-weight: bold;
            }

            /* تحسين مظهر القوائم المنسدلة */
            QComboBox {
                border: 3px solid #3498db;
                border-radius: 10px;
                padding: 10px 15px;
                background: white;
                font-size: 14pt;
                font-weight: bold;
                min-height: 30px;
            }

            QComboBox:hover {
                border: 3px solid #2980b9;
                background: #ecf0f1;
            }
        """)

    def enhance_table_appearance(self):
        """تحسين مظهر الجدول - نظام متطور من sub252_window.py"""
        if hasattr(self, 'table_lists'):
            # تحسين الخطوط والألوان
            font = QFont("Calibri", 13)
            font.setBold(True)
            self.table_lists.setFont(font)

            # تحسين ألوان التحديد
            palette = self.table_lists.palette()
            palette.setColor(QPalette.Highlight, QColor("#ffc107"))  # أصفر ذهبي
            palette.setColor(QPalette.HighlightedText, QColor("#2c3e50"))  # نص غامق
            self.table_lists.setPalette(palette)

            # تحسين مظهر الرأس
            header = self.table_lists.horizontalHeader()
            header_font = QFont("Calibri", 14)
            header_font.setBold(True)
            header.setFont(header_font)

            # تحسين ارتفاع الرأس - تقليل بمقدار الثلث إضافي
            header.setMinimumHeight(20)  # كان 30، أصبح 20 (تقليل إضافي 10 بكسل)

            # تحسين الحدود والتباعد
            self.table_lists.setShowGrid(True)
            self.table_lists.setGridStyle(Qt.SolidLine)

            # تحسين التمرير
            self.table_lists.setVerticalScrollMode(self.table_lists.ScrollPerPixel)
            self.table_lists.setHorizontalScrollMode(self.table_lists.ScrollPerPixel)

    def enhance_checkbox_appearance(self):
        """تحسين مظهر مربعات الاختيار - نظام متطور من sub252_window.py"""
        # تطبيق تنسيقات متقدمة لمربعات الاختيار
        checkbox_style = """
            QCheckBox {
                spacing: 8px;
                font-size: 14pt;
                font-weight: bold;
                color: #2c3e50;
            }

            QCheckBox::indicator {
                width: 25px;
                height: 25px;
                border-radius: 5px;
                border: 3px solid #bdc3c7;
                background-color: white;
            }

            QCheckBox::indicator:hover {
                border: 3px solid #3498db;
                background-color: #ecf0f1;
                box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
            }

            QCheckBox::indicator:checked {
                border: 3px solid #ffc107;
                background-color: #ffc107;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEzLjUgNEw2IDExLjVMMi41IDgiIHN0cm9rZT0iIzJjM2U1MCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
            }

            QCheckBox::indicator:checked:hover {
                border: 3px solid #f39c12;
                background-color: #f39c12;
                box-shadow: 0 4px 8px rgba(243, 156, 18, 0.4);
            }
        """

        # تطبيق التنسيق على جميع مربعات الاختيار في النافذة
        self.setStyleSheet(self.styleSheet() + checkbox_style)

    def add_button_animations(self):
        """إضافة تأثيرات للأزرار - محسن للتوافق مع PyQt5"""
        for _, button in self.buttons.items():
            # إضافة تأثيرات بصرية متوافقة مع PyQt5
            original_style = button.styleSheet()

            enhanced_style = original_style + """
                QPushButton:hover {
                    border: 3px solid #2c3e50;
                }

                QPushButton:pressed {
                    background: qlineargradient(
                        x1: 0, y1: 0, x2: 0, y2: 1,
                        stop: 0 #34495e,
                        stop: 1 #2c3e50
                    );
                }

                QPushButton:focus {
                    border: 3px solid #3498db;
                }
            """

            button.setStyleSheet(enhanced_style)

    def apply_beautiful_color_scheme(self):
        """تطبيق نظام ألوان جميل ومتقدم - من sub252_window.py"""
        # تحديد نظام الألوان الجميل
        color_scheme = {
            'primary': '#3498db',      # أزرق أساسي
            'secondary': '#2ecc71',    # أخضر ثانوي
            'accent': '#ffc107',       # أصفر مميز
            'danger': '#e74c3c',       # أحمر للتحذيرات
            'warning': '#f39c12',      # برتقالي للتنبيهات
            'info': '#17a2b8',         # أزرق فاتح للمعلومات
            'light': '#f8f9fa',        # رمادي فاتح
            'dark': '#2c3e50',         # رمادي غامق
            'success': '#28a745'       # أخضر للنجاح
        }

        # تطبيق نظام الألوان على النافذة
        beautiful_style = f"""
            /* نظام الألوان الجميل */
            QWidget {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 {color_scheme['light']},
                    stop: 0.5 #e9ecef,
                    stop: 1 #dee2e6
                );
                color: {color_scheme['dark']};
                font-family: 'Calibri', 'Arial', sans-serif;
            }}

            /* تحسين مظهر الجداول بالألوان الجميلة */
            QTableView {{
                background-color: white;
                border: 3px solid {color_scheme['primary']};
                border-radius: 12px;
                gridline-color: #ecf0f1;
                selection-background-color: {color_scheme['accent']};
                selection-color: {color_scheme['dark']};
                font-size: 13pt;
                font-weight: bold;
            }}

            QTableView::item:hover {{
                background-color: #e8f4fd;
                color: {color_scheme['dark']};
            }}

            /* تحسين رأس الجدول */
            QHeaderView::section {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color_scheme['primary']},
                    stop: 1 #2980b9
                );
                color: white;
                padding: 12px;
                border: 1px solid #2980b9;
                font-weight: bold;
                font-size: 14pt;
            }}

            QHeaderView::section:hover {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5dade2,
                    stop: 1 {color_scheme['primary']}
                );
            }}

            /* تحسين القوائم المنسدلة */
            QComboBox {{
                background: white;
                border: 3px solid {color_scheme['secondary']};
                border-radius: 10px;
                padding: 10px 15px;
                font-size: 14pt;
                font-weight: bold;
                min-height: 30px;
            }}

            QComboBox:hover {{
                border: 3px solid #27ae60;
                background: #e8f5e8;
            }}

            /* تحسين حقول التاريخ والوقت */
            QDateEdit, QTimeEdit {{
                background: white;
                border: 3px solid {color_scheme['info']};
                border-radius: 10px;
                padding: 10px 15px;
                font-size: 14pt;
                font-weight: bold;
                min-height: 30px;
            }}

            QDateEdit:hover, QTimeEdit:hover {{
                border: 3px solid #138496;
                background: #e1f7fa;
            }}
        """

        # تطبيق نظام الألوان
        self.setStyleSheet(self.styleSheet() + beautiful_style)

    def initUI(self):
        # تعيين عنوان النافذة
        self.setWindowTitle("نافذة بحث")

        # الحصول على حجم الشاشة واستخدامه لضبط حجم النافذة
        screen_size = QApplication.desktop().screenGeometry()
        screen_width = screen_size.width()
        screen_height = screen_size.height()

        # تعيين حجم ومكان النافذة (تقريباً كامل الشاشة)
        width = int(screen_width * 0.98)
        height = int(screen_height * 0.95)
        x = int((screen_width - width) / 2)
        y = int((screen_height - height) / 2)

        self.setGeometry(x, y, width, height)
        self.setMinimumSize(980, 600)

        # جعل النافذة تأخذ كامل الشاشة عند الفتح
        self.showMaximized()

        # تم إزالة مؤقت التلميحات المخصصة واستبداله بتلميحات HTML مباشرة

        # إزالة التخطيط الرئيسي واستبداله بتخطيط أبسط مباشرة
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(5)

        # تحديث تخطيط الصف العلوي
        top_row_layout = QHBoxLayout()
        top_row_layout.setSpacing(5)

        # إضافة الأدوات في الجزء الأيمن
        right_tools = QHBoxLayout()
        right_tools.setSpacing(5)

        self.guard_combo = QComboBox()
        self.guard_combo.setObjectName("GuardComboBox")
        self.guard_combo.setFont(self.font_calibri_12_normal)
        self.guard_combo.setFixedWidth(240)  # مضاعفة العرض من 120 إلى 240 بكسل
        # إضافة بيانات الحراس
        self.guard_combo.addItems(["حراسة رقم 1", "حراسة رقم 2", "حراسة رقم 3", "حراسة رقم 4", "حراسة رقم 5"])
        right_tools.addWidget(self.guard_combo)

        # إضافة زر تعديل التاريخ والوقت
        self.datetime_button = QPushButton("تعديل التاريخ والوقت")
        self.datetime_button.setFont(self.font_calibri_12_normal)
        self.datetime_button.setStyleSheet("""
            background-color: #4a86e8;
            color: white;
            font-weight: bold;
            border-radius: 5px;
            padding: 5px;
        """)
        self.datetime_button.setFixedWidth(140)
        self.datetime_button.setCursor(Qt.PointingHandCursor)
        self.datetime_button.clicked.connect(self.show_datetime_dialog)
        right_tools.addWidget(self.datetime_button)

        # تخزين التاريخ والوقت الحاليين كقيم افتراضية
        self.current_date = QDate.currentDate()
        self.current_time = QTime.currentTime()
        self.use_custom_datetime = False
        top_row_layout.addLayout(right_tools)

        # خط فاصل عمودي
        sep_line = QFrame()
        sep_line.setFrameShape(QFrame.VLine)
        sep_line.setFrameShadow(QFrame.Sunken)
        top_row_layout.addWidget(sep_line)

        # إضافة الأزرار المحسنة مع النظام المتطور
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(5)

        # تعريف الأزرار العادية مع ألوانها المميزة
        buttons_config = [
            ("ورقة الدخول", "#27ae60"),      # أخضر
            ("ورقة التأخر", "#e74c3c"),       # أحمر
            ("الرمز السري", "#ff6b35"),       # برتقالي مميز جديد
            ("مسك الطلبات", "#16a085"),       # أخضر مزرق
            ("بطاقة اللوائح", "#d35400"),     # برتقالي محمر
            ("تحديث", "#3498db")             # أزرق فاتح
        ]

        self.buttons = {}

        # إنشاء الأزرار العادية
        for btn_text, btn_color in buttons_config:
            button = self.create_action_button(btn_text, btn_color)
            button.setCursor(Qt.PointingHandCursor)
            buttons_layout.addWidget(button)
            self.buttons[btn_text] = button

        # إنشاء زر القائمة المنسدلة للأوراق والتصدير
        self.create_dropdown_button(buttons_layout)

        # إنشاء زر بطاقة تلميذ مع القائمة المنسدلة
        self.create_student_card_button(buttons_layout)

        top_row_layout.addLayout(buttons_layout)
        top_row_layout.addStretch()
        main_layout.addLayout(top_row_layout)

        content_layout = QHBoxLayout()
        content_layout.setSpacing(10)

        # إنشاء جدول البيانات مباشرة بدون إطارات إضافية
        self.table_lists = QTableView()
        self.table_lists.setObjectName("ListsTable")
        self.table_lists.setSortingEnabled(True)
        self.table_lists.setAlternatingRowColors(True)
        self.table_lists.setFont(self.font_calibri_13_normal)
        self.table_lists.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table_lists.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.table_lists.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.model_lists = QSqlQueryModel()
        self.table_lists.setModel(self.model_lists)
        self.blue_delegate = BlueTextDelegate(self.table_lists)
        self.table_lists.setItemDelegateForColumn(self.code_column_index, self.blue_delegate)

        # تعيين تلميح للجدول يظهر بعد 5 ثوان
        self.table_lists.setToolTipDuration(5000)  # 5000 مللي ثانية = 5 ثوان

        # تعيين مؤشر اليد فقط لأعمدة محددة (مربع الاختيار والرمز)
        class ColumnCursorDelegate(QStyledItemDelegate):
            def editorEvent(self, event, model, option, index):
                if event.type() == event.Enter:
                    QApplication.setOverrideCursor(Qt.PointingHandCursor)
                elif event.type() == event.Leave:
                    QApplication.restoreOverrideCursor()
                return super().editorEvent(event, model, option, index)

        # تطبيق مندوب المؤشر على الأعمدة المحددة
        cursor_delegate = ColumnCursorDelegate(self.table_lists)
        self.table_lists.setItemDelegateForColumn(0, cursor_delegate)  # عمود مربع الاختيار
        self.table_lists.setItemDelegateForColumn(self.code_column_index, cursor_delegate)  # عمود الرمز

        content_layout.addWidget(self.table_lists, 5)

        right_panel_layout = QVBoxLayout()
        right_panel_layout.setSpacing(10)

        # تخطيط المستويات والفصول
        self.table_levels = QTableView()
        self.table_levels.setObjectName("LevelsTable")
        self.table_levels.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.table_levels.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table_levels.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table_levels.verticalHeader().setVisible(False)
        self.table_levels.horizontalHeader().setStretchLastSection(True)
        self.table_levels.setFont(self.font_calibri_13_bold)
        self.table_levels.setCursor(Qt.PointingHandCursor)  # تغيير مؤشر الفأرة إلى يد عند المرور فوق الجدول
        self.model_levels = QSqlQueryModel()
        self.table_levels.setModel(self.model_levels)

        self.table_classes = QTableView()
        self.table_classes.setObjectName("ClassesTable")
        self.table_classes.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.table_classes.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table_classes.setSelectionMode(QAbstractItemView.SingleSelection)
        self.table_classes.verticalHeader().setVisible(False)
        self.table_classes.horizontalHeader().setStretchLastSection(True)
        self.table_classes.setFont(self.font_calibri_13_bold)
        self.table_classes.setCursor(Qt.PointingHandCursor)  # تغيير مؤشر الفأرة إلى يد عند المرور فوق الجدول
        self.model_classes = QSqlQueryModel()
        self.table_classes.setModel(self.model_classes)

        right_panel_layout.addWidget(self.table_levels)
        right_panel_layout.addWidget(self.table_classes)
        content_layout.addLayout(right_panel_layout, 2)

        main_layout.addLayout(content_layout)

    def show_datetime_dialog(self):
        """عرض نافذة لإدخال التاريخ والوقت المخصص"""
        # إنشاء نافذة حوار مخصصة
        datetime_dialog = QDialog(self)
        datetime_dialog.setWindowTitle("تعديل التاريخ والوقت")
        datetime_dialog.setFixedSize(450, 400)
        datetime_dialog.setLayoutDirection(Qt.RightToLeft)
        datetime_dialog.setWindowIcon(QIcon("01.ico"))

        # تنسيق النافذة
        datetime_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #4a86e8;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
                min-height: 40px;
                min-width: 120px;
            }
            QPushButton#useCurrentButton {
                background-color: #4a86e8;
                color: white;
            }
            QPushButton#useCurrentButton:hover {
                background-color: #3a76d8;
            }
            QPushButton#useCustomButton {
                background-color: #4CAF50;
                color: white;
            }
            QPushButton#useCustomButton:hover {
                background-color: #3c9f40;
            }
            QPushButton#cancelButton {
                background-color: #f44336;
                color: white;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton#cancelButton:hover {
                background-color: #e43326;
                border: 1px solid #c62828;
            }
            QDateEdit, QTimeEdit {
                font-family: Calibri;
                font-size: 14pt;
                padding: 8px;
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 5px;
                min-height: 40px;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(datetime_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة عنوان
        title_label = QLabel("تعديل التاريخ والوقت")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #4a86e8; border-bottom: 2px solid #4a86e8; padding-bottom: 10px;")
        layout.addWidget(title_label)

        # إضافة وصف
        description_label = QLabel("يمكنك استخدام التاريخ والوقت الحاليين أو تحديد تاريخ ووقت مخصصين:")
        description_label.setFont(QFont("Calibri", 13))
        description_label.setWordWrap(True)
        layout.addWidget(description_label)

        # إضافة حقول التاريخ والوقت
        date_layout = QHBoxLayout()
        date_label = QLabel("التاريخ:")
        date_label.setFont(QFont("Calibri", 13, QFont.Bold))
        date_edit = QDateEdit()
        date_edit.setCalendarPopup(True)
        date_edit.setDate(self.current_date)
        date_edit.setFont(QFont("Calibri", 13))
        date_layout.addWidget(date_label)
        date_layout.addWidget(date_edit)
        layout.addLayout(date_layout)

        time_layout = QHBoxLayout()
        time_label = QLabel("الوقت:")
        time_label.setFont(QFont("Calibri", 13, QFont.Bold))
        time_edit = QTimeEdit()
        time_edit.setTime(self.current_time)
        time_edit.setFont(QFont("Calibri", 13))
        time_layout.addWidget(time_label)
        time_layout.addWidget(time_edit)
        layout.addLayout(time_layout)

        # إضافة أزرار في تخطيط عمودي لتجنب التداخل
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(10)

        # زر استخدام الوقت الحالي
        use_current_button = QPushButton("استخدام الوقت الحالي")
        use_current_button.setObjectName("useCurrentButton")
        use_current_button.setFont(QFont("Calibri", 12, QFont.Bold))
        use_current_button.setCursor(Qt.PointingHandCursor)
        use_current_button.setFixedHeight(40)
        use_current_button.clicked.connect(lambda: self.set_datetime_choice(datetime_dialog, True))
        buttons_layout.addWidget(use_current_button)

        # زر استخدام الوقت المخصص
        use_custom_button = QPushButton("استخدام الوقت المخصص")
        use_custom_button.setObjectName("useCustomButton")
        use_custom_button.setFont(QFont("Calibri", 12, QFont.Bold))
        use_custom_button.setCursor(Qt.PointingHandCursor)
        use_custom_button.setFixedHeight(40)
        use_custom_button.clicked.connect(lambda: self.set_datetime_choice(datetime_dialog, False, date_edit.date(), time_edit.time()))
        buttons_layout.addWidget(use_custom_button)

        # إضافة مسافة قبل زر الإغلاق
        buttons_layout.addSpacing(10)

        # زر الإغلاق (مصغر)
        cancel_layout = QHBoxLayout()
        cancel_button = QPushButton("إغلاق")
        cancel_button.setObjectName("cancelButton")
        cancel_button.setFont(QFont("Calibri", 11))
        cancel_button.setCursor(Qt.PointingHandCursor)
        cancel_button.setFixedWidth(100)
        cancel_button.setFixedHeight(30)
        cancel_button.clicked.connect(datetime_dialog.reject)

        cancel_layout.addStretch()
        cancel_layout.addWidget(cancel_button)
        cancel_layout.addStretch()

        buttons_layout.addLayout(cancel_layout)

        layout.addLayout(buttons_layout)

        # عرض النافذة
        datetime_dialog.exec_()

    def set_datetime_choice(self, dialog, use_current, custom_date=None, custom_time=None):
        """تعيين اختيار التاريخ والوقت"""
        if use_current:
            self.current_date = QDate.currentDate()
            self.current_time = QTime.currentTime()
            self.use_custom_datetime = False
            self.datetime_button.setText("تعديل التاريخ والوقت")
            self.datetime_button.setStyleSheet("""
                background-color: #4a86e8;
                color: white;
                font-weight: bold;
                border-radius: 5px;
                padding: 5px;
            """)
        else:
            self.current_date = custom_date
            self.current_time = custom_time
            self.use_custom_datetime = True
            self.datetime_button.setText("تاريخ ووقت مخصص")
            self.datetime_button.setStyleSheet("""
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                border-radius: 5px;
                padding: 5px;
            """)

        # إظهار رسالة نجاح
        self.show_custom_success_message("تم تعيين التاريخ والوقت بنجاح")
        dialog.accept()

    def refresh_data(self):
        """تحديث البيانات في النافذة عند استلام إشارة التحديث"""
        try:
            print("جاري تحديث بيانات نافذة البحث...")

            # تحديث البيانات في الجداول
            self.load_initial_data()

            # إعادة تحميل المستويات والأقسام
            if self.selected_level:
                self.on_level_selected(None, None)

                # إعادة تحميل الأقسام إذا كان هناك مستوى محدد
                if self.selected_class:
                    self.on_class_selected(None, None)

            print("تم تحديث بيانات نافذة البحث بنجاح")
            return True
        except Exception as e:
            print(f"خطأ في تحديث بيانات نافذة البحث: {str(e)}")
            return False

    def connect_signals(self):
        self.table_levels.selectionModel().selectionChanged.connect(self.on_level_selected)
        self.table_classes.selectionModel().selectionChanged.connect(self.on_class_selected)
        self.buttons["ورقة الدخول"].clicked.connect(self.add_to_entry_sheet)
        self.buttons["ورقة التأخر"].clicked.connect(self.add_to_late_sheet)
        self.buttons["الرمز السري"].clicked.connect(self.print_secret_code)
        self.buttons["مسك الطلبات"].clicked.connect(self.open_absence_justification)
        self.buttons["بطاقة اللوائح"].clicked.connect(self.show_regulations_card)
        self.buttons["تحديث"].clicked.connect(self.update_data)
        # ملاحظة: أزرار ورقة التوجيه، ورقة الاستئذان، زيارة الطبيب، والتصدير وتعليمات تم نقلها للقائمة المنسدلة
        self.guard_combo.currentTextChanged.connect(self.filter_levels_by_guard)
        self.table_lists.clicked.connect(self.on_table_cell_clicked)

        # إضافة تلميحات مفصلة للأزرار
        self.add_detailed_tooltips()

    def show_styled_message_box(self, title, message, icon=QMessageBox.Information,
                           button_color="#4CAF50", button_hover_color="#45a049",
                           use_html=False, detailed_text=None):
        """
        عرض رسالة منسقة بشكل موحد

        المعلمات:
            title (str): عنوان الرسالة
            message (str): نص الرسالة
            icon (QMessageBox.Icon): أيقونة الرسالة
            button_color (str): لون زر موافق
            button_hover_color (str): لون زر موافق عند التحويم
            use_html (bool): استخدام تنسيق HTML للرسالة
            detailed_text (str): نص تفصيلي إضافي (اختياري)
        """
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setIcon(icon)

        # تعيين أيقونة البرنامج
        msg_box.setWindowIcon(QIcon("01.ico"))

        # إعداد نص الرسالة
        if use_html:
            msg_box.setText(message)
        else:
            # إنشاء نص الرسالة بتنسيق HTML
            html_message = f"""
            <div style='text-align: right; direction: rtl;'>
                <p style='font-family: Calibri; font-size: 14pt; font-weight: bold;'>
                    {message}
                </p>
            </div>
            """
            msg_box.setText(html_message)

        # إضافة نص تفصيلي إذا كان متوفراً
        if detailed_text:
            msg_box.setDetailedText(detailed_text)

        # تعيين أحجام الأزرار وتنسيقها
        ok_button = msg_box.addButton(QMessageBox.Ok)
        ok_button.setText("موافق")
        ok_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {button_color};
                color: white;
                font-weight: bold;
                border-radius: 5px;
                padding: 8px 16px;
                min-width: 100px;
                min-height: 30px;
                font-family: Calibri;
                font-size: 13pt;
            }}
            QPushButton:hover {{
                background-color: {button_hover_color};
            }}
        """)

        # تنسيق الرسالة
        msg_box.setStyleSheet("""
            QMessageBox {
                font-family: Calibri;
                font-size: 13pt;
            }
            QTextEdit {
                font-family: Calibri;
                font-size: 12pt;
            }
        """)

        return msg_box.exec_()

    def show_custom_success_message(self, message, title="تم بنجاح"):
        """عرض رسالة نجاح مخصصة"""
        # استخدام دالة عرض الرسائل المخصصة من وحدة sub100_window إذا كانت متوفرة
        if CUSTOM_DIALOGS_IMPORTED:
            ConfirmationDialogs.show_custom_success_message(self, message, title)
        else:
            # استخدام الطريقة المنسقة الجديدة
            self.show_styled_message_box(
                title=title,
                message=message,
                icon=QMessageBox.Information,
                button_color="#27ae60",
                button_hover_color="#2ecc71",
                use_html=True
            )

    def update_main_table(self):
        """تحديث جدول_عام بالبيانات من جدول السجل_العام وجدول اللوائح"""
        try:
            print("\n" + "="*50)
            print("بدء عملية تحديث جدول_عام")
            print("="*50)

            # حذف جميع البيانات من جدول_عام قبل التحديث
            delete_query = QSqlQuery(db=self.db)
            if delete_query.exec_("DELETE FROM جدول_عام"):
                print("تم حذف جميع البيانات من جدول_عام قبل التحديث")
            else:
                print(f"خطأ في حذف البيانات من جدول_عام: {delete_query.lastError().text()}")

            # الحصول على السنة الدراسية والأسدس من جدول بيانات_المؤسسة
            current_academic_year = self.current_academic_year
            current_semester = None

            semester_query = QSqlQuery(db=self.db)
            if semester_query.exec_("SELECT الأسدس FROM بيانات_المؤسسة LIMIT 1"):
                if semester_query.next():
                    current_semester = semester_query.value(0)
                    print(f"تم الحصول على الأسدس الحالي: {current_semester}")
                else:
                    print("لم يتم العثور على بيانات في جدول بيانات_المؤسسة")
                    current_semester = "الأول"  # قيمة افتراضية
            else:
                print(f"خطأ في الحصول على الأسدس: {semester_query.lastError().text()}")
                current_semester = "الأول"  # قيمة افتراضية

            # بناء استعلام لتحديث جدول_عام
            insert_query = QSqlQuery(db=self.db)
            query_str = """
                INSERT INTO جدول_عام (
                    الرمز, الاسم_والنسب, النوع, تاريخ_الازدياد, مكان_الازدياد,
                    السن, الرمز_السري, الهاتف_الأول, الهاتف_الثاني, عدد_المخالفات,
                    التأخر, السماح, الغياب, ملاحظات, السنة_الدراسية, الأسدس,
                    القسم, المستوى, رت
                )
                SELECT
                    s.الرمز, s.الاسم_والنسب, s.النوع, s.تاريخ_الازدياد, s.مكان_الازدياد,
                    CASE WHEN s.تاريخ_الازدياد IS NULL OR s.تاريخ_الازدياد = '' THEN 0
                         ELSE (strftime('%Y', 'now') - strftime('%Y', s.تاريخ_الازدياد)) -
                              (strftime('%m-%d', 'now') < strftime('%m-%d', s.تاريخ_الازدياد))
                    END as السن,
                    s.الرمز_السري, s.الهاتف_الأول, s.الهاتف_الثاني, s.عدد_المخالفات,
                    s.التأخر, s.السماح,
                    COALESCE((
                        SELECT SUM(CAST(COALESCE(غياب_غير_مبرر, '0') AS INTEGER))
                        FROM مسك_الغياب_الأسبوعي
                        WHERE رمز_التلميذ = s.الرمز
                        AND الأسدس = ?
                        AND السنة_الدراسية = ?
                    ), 0) as الغياب,
                    '' as ملاحظات, ?, ?, l.القسم, l.المستوى, l.رت
                FROM السجل_العام s
                JOIN اللوائح l ON s.الرمز = l.الرمز
            """

            insert_query.prepare(query_str)
            insert_query.addBindValue(current_semester)
            insert_query.addBindValue(current_academic_year)
            insert_query.addBindValue(current_academic_year)
            insert_query.addBindValue(current_semester)

            if insert_query.exec_():
                print(f"تم تحديث جدول_عام بنجاح")
            else:
                print(f"خطأ في تحديث جدول_عام: {insert_query.lastError().text()}")

            print("="*50)
            print("انتهت عملية تحديث جدول_عام")
            print("="*50)

            return True
        except Exception as e:
            print(f"حدث خطأ أثناء تحديث جدول_عام: {str(e)}")
            return False

    def show_regulations_card(self):
        """فتح نافذة بطاقة اللوائح (sub20_window.py) كنافذة مشروطة في وسط الشاشة"""
        # التحقق من تحديد قسم فقط (لا يشترط تحديد تلاميذ)
        section = self.get_selected_section()
        if not section:
            self.show_styled_message_box(
                title="تنبيه",
                message="الرجاء تحديد قسم أولاً.",
                icon=QMessageBox.Warning,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )
            return

        try:
            # تحديث جدول_عام قبل فتح نافذة بطاقة اللوائح
            print("جاري تحديث جدول_عام قبل فتح بطاقة اللوائح...")
            update_success = self.update_main_table()

            if update_success:
                print("تم تحديث جدول_عام بنجاح")
            else:
                print("حدث خطأ أثناء تحديث جدول_عام")

            # استيراد نافذة بطاقة اللوائح
            sub20_module = import_module_by_name("sub20_window")
            if sub20_module and hasattr(sub20_module, "RegulationsCardWindow"):
                # إنشاء نافذة بطاقة اللوائح
                regulations_window = sub20_module.RegulationsCardWindow(
                    parent=self,
                    db=self.db,
                    section=section,
                    academic_year=self.current_academic_year
                )

                # تحديد موقع النافذة في وسط الشاشة
                screen_geometry = QApplication.desktop().screenGeometry()
                x = (screen_geometry.width() - regulations_window.width()) // 2
                y = (screen_geometry.height() - regulations_window.height()) // 2
                regulations_window.move(x, y)

                # عرض النافذة كنافذة مشروطة (modal)
                regulations_window.setWindowModality(Qt.ApplicationModal)
                regulations_window.show()

                # تم إزالة رسالة النجاح لتجنب ظهور نوافذ منبثقة
                print(f"تم فتح بطاقة اللوائح للقسم {section} بنجاح")
            else:
                self.show_styled_message_box(
                    title="تنبيه",
                    message="نافذة بطاقة اللوائح غير متوفرة. تأكد من وجود ملف sub20_window.py.",
                    icon=QMessageBox.Warning,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
        except Exception as e:
            self.show_styled_message_box(
                title="خطأ",
                message=f"حدث خطأ أثناء فتح نافذة بطاقة اللوائح:\n{str(e)}",
                icon=QMessageBox.Critical,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )

    def add_detailed_tooltips(self):
        """إضافة تلميحات مفصلة للأزرار باستخدام HTML"""
        # تلميح زر ورقة الدخول
        entry_tooltip = """
        <div dir='rtl' style='text-align: right; direction: rtl; width: 400px; padding: 15px; background-color: #e6f2ff; border: 2px solid #0066cc; border-radius: 8px;'>
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td align="center">
                        <p style='font-family: Calibri; font-size: 16pt; color: #0066cc; font-weight: bold; text-align: center; border-bottom: 2px solid #0066cc; padding-bottom: 5px; margin: 0;'>
                            تأكيد السماح بالدخول
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 10px;">
                        <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; margin: 0; text-align: justify;'>
                            يستخدم هذا الزر لتسجيل وطباعة ورقة السماح بالدخول للتلاميذ المتأخرين أو الذين يحتاجون إلى إذن خاص للدخول إلى المؤسسة.
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #0066cc; font-weight: bold; border-bottom: 1px solid #0066cc; padding-bottom: 3px; margin: 0;'>
                            خطوات الاستخدام:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ol style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>حدد المستوى المطلوب من الجدول الأيمن.</li>
                            <li>حدد القسم المطلوب من الجدول الأوسط.</li>
                            <li>حدد التلميذ أو التلاميذ المطلوبين بالنقر على مربع الاختيار.</li>
                            <li>تأكد من تحديد التاريخ والوقت المناسبين.</li>
                            <li>انقر على زر "ورقة الدخول" لتسجيل وطباعة ورقة السماح بالدخول.</li>
                        </ol>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #0066cc; font-weight: bold; border-bottom: 1px solid #0066cc; padding-bottom: 3px; margin: 0;'>
                            ملاحظات هامة:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ul style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>يمكنك تحديد عدة تلاميذ في نفس الوقت لطباعة أوراق متعددة.</li>
                            <li>سيتم تسجيل عملية الدخول في قاعدة البيانات مع التاريخ والوقت.</li>
                            <li>تأكد من توصيل الطابعة قبل النقر على الزر.</li>
                        </ul>
                    </td>
                </tr>
            </table>
        </div>
        """
        self.buttons["ورقة الدخول"].setToolTip(entry_tooltip)

        # تلميح زر ورقة التأخر
        late_tooltip = """
        <div dir='rtl' style='text-align: right; direction: rtl; width: 400px; padding: 15px; background-color: #e6f2ff; border: 2px solid #0066cc; border-radius: 8px;'>
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td align="center">
                        <p style='font-family: Calibri; font-size: 16pt; color: #0066cc; font-weight: bold; text-align: center; border-bottom: 2px solid #0066cc; padding-bottom: 5px; margin: 0;'>
                            تأكيد تسجيل التأخر
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 10px;">
                        <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; margin: 0; text-align: justify;'>
                            يستخدم هذا الزر لتسجيل وطباعة ورقة التأخر للتلاميذ الذين حضروا متأخرين عن موعد بدء الدراسة.
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #0066cc; font-weight: bold; border-bottom: 1px solid #0066cc; padding-bottom: 3px; margin: 0;'>
                            خطوات الاستخدام:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ol style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>حدد المستوى المطلوب من الجدول الأيمن.</li>
                            <li>حدد القسم المطلوب من الجدول الأوسط.</li>
                            <li>حدد التلميذ أو التلاميذ المتأخرين بالنقر على مربع الاختيار.</li>
                            <li>تأكد من تحديد التاريخ والوقت المناسبين.</li>
                            <li>انقر على زر "ورقة التأخر" لتسجيل وطباعة ورقة التأخر.</li>
                        </ol>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #0066cc; font-weight: bold; border-bottom: 1px solid #0066cc; padding-bottom: 3px; margin: 0;'>
                            ملاحظات هامة:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ul style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>يمكنك تحديد عدة تلاميذ في نفس الوقت لطباعة أوراق متعددة.</li>
                            <li>سيتم تسجيل عملية التأخر في قاعدة البيانات مع التاريخ والوقت.</li>
                            <li>تأكد من توصيل الطابعة قبل النقر على الزر.</li>
                        </ul>
                    </td>
                </tr>
            </table>
        </div>
        """
        self.buttons["ورقة التأخر"].setToolTip(late_tooltip)

        # تلميح زر الأوراق والتصدير (القائمة المنسدلة الجديدة)
        dropdown_tooltip = """
        <div dir='rtl' style='text-align: right; direction: rtl; width: 400px; padding: 15px; background-color: #f0f8ff; border: 2px solid #8e44ad; border-radius: 8px;'>
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td align="center">
                        <p style='font-family: Calibri; font-size: 16pt; color: #8e44ad; font-weight: bold; text-align: center; border-bottom: 2px solid #8e44ad; padding-bottom: 5px; margin: 0;'>
                            📋 الأوراق والتصدير
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 10px;">
                        <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; margin: 0; text-align: justify;'>
                            قائمة منسدلة تحتوي على جميع أنواع الأوراق والتصدير المتاحة.
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #8e44ad; font-weight: bold; border-bottom: 1px solid #8e44ad; padding-bottom: 3px; margin: 0;'>
                            الخيارات المتاحة:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ul style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>📋 ورقة توجيه - لتوجيه التلاميذ لأقسام معينة</li>
                            <li>📝 ورقة استئذان - لاستئذان التلاميذ</li>
                            <li>🏥 زيارة الطبيب - لطباعة نموذج زيارة الطبيب</li>
                            <li>📊 تصدير البيانات - لتصدير بيانات التلاميذ المحددين</li>
                            <li>❓ تعليمات - لعرض نافذة التعليمات</li>
                        </ul>
                    </td>
                </tr>
            </table>
        </div>
        """
        self.buttons["الأوراق والتصدير"].setToolTip(dropdown_tooltip)

        # تلميح زر بطاقة تلميذ (القائمة المنسدلة الجديدة)
        student_card_tooltip = """
        <div dir='rtl' style='text-align: right; direction: rtl; width: 450px; padding: 15px; background-color: #fff8f0; border: 2px solid #e67e22; border-radius: 8px;'>
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td align="center">
                        <p style='font-family: Calibri; font-size: 16pt; color: #e67e22; font-weight: bold; text-align: center; border-bottom: 2px solid #e67e22; padding-bottom: 5px; margin: 0;'>
                            👤 بطاقة تلميذ
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 10px;">
                        <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; margin: 5px 0; text-align: justify;'>
                            قائمة منسدلة شاملة لجميع عمليات إدارة بيانات التلميذ:
                        </p>
                        <ul style='font-family: Calibri; font-size: 12pt; color: #2c3e50; margin: 10px 0; padding-right: 20px;'>
                            <li style='margin: 5px 0;'><strong>👤 بيانات التلميذ:</strong> عرض البيانات الشخصية والأكاديمية</li>
                            <li style='margin: 5px 0;'><strong>📋 سجلات الدخول والتأخر:</strong> مراجعة سجلات الحضور</li>
                            <li style='margin: 5px 0;'><strong>⚠️ مسك المخالفات:</strong> إدارة ومعالجة المخالفات</li>
                            <li style='margin: 5px 0;'><strong>📝 تبرير الغياب:</strong> مسك ومعالجة تبريرات الغياب</li>
                            <li style='margin: 5px 0;'><strong>👨‍👩‍👧‍👦 زيارة أولياء الأمور:</strong> توثيق الزيارات والاجتماعات</li>
                        </ul>
                        <p style='font-family: Calibri; font-size: 11pt; color: #7f8c8d; font-style: italic; margin: 10px 0 0 0; text-align: center;'>
                            يرجى تحديد تلميذ واحد على الأقل قبل استخدام هذه الخيارات
                        </p>
                    </td>
                </tr>
            </table>
        </div>
        """
        self.buttons["بطاقة تلميذ"].setToolTip(student_card_tooltip)

        # ملاحظة: تم نقل ورقة الاستئذان للقائمة المنسدلة

        # تلميح زر الرمز السري للتلميذ
        secret_code_tooltip = """
        <div dir='rtl' style='text-align: right; direction: rtl; width: 400px; padding: 15px; background-color: #f0f8ff; border: 2px solid #0066cc; border-radius: 8px;'>
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td align="center">
                        <p style='font-family: Calibri; font-size: 16pt; color: #0066cc; font-weight: bold; text-align: center; border-bottom: 2px solid #0066cc; padding-bottom: 5px; margin: 0;'>
                            الرمز السري للتلميذ(ة)
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 10px;">
                        <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; margin: 0; text-align: justify;'>
                            يستخدم هذا الزر لطباعة الرموز السرية للتلاميذ المحددين، والتي يمكن استخدامها للدخول إلى المنصات الإلكترونية.
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #0066cc; font-weight: bold; border-bottom: 1px solid #0066cc; padding-bottom: 3px; margin: 0;'>
                            خطوات الاستخدام:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ol style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>حدد المستوى المطلوب من الجدول الأيمن.</li>
                            <li>حدد القسم المطلوب من الجدول الأوسط.</li>
                            <li>حدد التلميذ أو التلاميذ المطلوبين بالنقر على مربع الاختيار.</li>
                            <li>انقر على زر "الرمز السري" لطباعة الرموز السرية.</li>
                        </ol>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #0066cc; font-weight: bold; border-bottom: 1px solid #0066cc; padding-bottom: 3px; margin: 0;'>
                            ملاحظات هامة:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ul style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>يمكنك تحديد عدة تلاميذ في نفس الوقت لطباعة أوراق متعددة.</li>
                            <li>تأكد من توصيل الطابعة قبل النقر على الزر.</li>
                            <li>الرموز السرية معلومات حساسة، تأكد من تسليمها للتلاميذ بشكل آمن.</li>
                        </ul>
                    </td>
                </tr>
            </table>
        </div>
        """
        self.buttons["الرمز السري"].setToolTip(secret_code_tooltip)

        # تلميح زر مسك الطلبات
        requests_tooltip = """
        <div dir='rtl' style='text-align: right; direction: rtl; width: 400px; padding: 15px; background-color: #f0f8ff; border: 2px solid #0066cc; border-radius: 8px;'>
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td align="center">
                        <p style='font-family: Calibri; font-size: 16pt; color: #0066cc; font-weight: bold; text-align: center; border-bottom: 2px solid #0066cc; padding-bottom: 5px; margin: 0;'>
                            مسك الطلبات
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 10px;">
                        <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; margin: 0; text-align: justify;'>
                            يستخدم هذا الزر لإضافة طلبات الشهادة المدرسية للتلاميذ المحددين وتسجيلها في قاعدة البيانات.
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #0066cc; font-weight: bold; border-bottom: 1px solid #0066cc; padding-bottom: 3px; margin: 0;'>
                            خطوات الاستخدام:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ol style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>حدد المستوى المطلوب من الجدول الأيمن.</li>
                            <li>حدد القسم المطلوب من الجدول الأوسط.</li>
                            <li>حدد التلميذ أو التلاميذ المطلوبين بالنقر على مربع الاختيار.</li>
                            <li>انقر على زر "مسك الطلبات" لإضافة طلبات الشهادة المدرسية.</li>
                        </ol>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #0066cc; font-weight: bold; border-bottom: 1px solid #0066cc; padding-bottom: 3px; margin: 0;'>
                            ملاحظات هامة:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ul style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>يمكنك تحديد عدة تلاميذ في نفس الوقت لإضافة طلبات متعددة.</li>
                            <li>سيتم تسجيل الطلبات في قاعدة البيانات مع التاريخ الحالي.</li>
                        </ul>
                    </td>
                </tr>
            </table>
        </div>
        """
        self.buttons["مسك الطلبات"].setToolTip(requests_tooltip)

        # تلميح زر زيارة الطبيب
        doctor_tooltip = """
        <div dir='rtl' style='text-align: right; direction: rtl; width: 400px; padding: 15px; background-color: #f0f8ff; border: 2px solid #0066cc; border-radius: 8px;'>
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td align="center">
                        <p style='font-family: Calibri; font-size: 16pt; color: #0066cc; font-weight: bold; text-align: center; border-bottom: 2px solid #0066cc; padding-bottom: 5px; margin: 0;'>
                            زيارة الطبيب
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 10px;">
                        <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; margin: 0; text-align: justify;'>
                            يستخدم هذا الزر لطباعة نموذج زيارة الطبيب للتلاميذ الذين يحتاجون إلى رعاية طبية.
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #0066cc; font-weight: bold; border-bottom: 1px solid #0066cc; padding-bottom: 3px; margin: 0;'>
                            خطوات الاستخدام:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ol style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>حدد المستوى المطلوب من الجدول الأيمن.</li>
                            <li>حدد القسم المطلوب من الجدول الأوسط.</li>
                            <li>حدد التلميذ أو التلاميذ المطلوبين بالنقر على مربع الاختيار.</li>
                            <li>انقر على زر "زيارة الطبيب" لطباعة نموذج زيارة الطبيب.</li>
                        </ol>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #0066cc; font-weight: bold; border-bottom: 1px solid #0066cc; padding-bottom: 3px; margin: 0;'>
                            ملاحظات هامة:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ul style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>يمكنك تحديد عدة تلاميذ في نفس الوقت لطباعة نماذج متعددة.</li>
                            <li>تأكد من توصيل الطابعة قبل النقر على الزر.</li>
                        </ul>
                    </td>
                </tr>
            </table>
        </div>
        """
        # ملاحظة: تم نقل زيارة الطبيب للقائمة المنسدلة

        # تلميح زر تصدير
        export_tooltip = """
        <div dir='rtl' style='text-align: right; direction: rtl; width: 400px; padding: 15px; background-color: #f0f8ff; border: 2px solid #0066cc; border-radius: 8px;'>
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td align="center">
                        <p style='font-family: Calibri; font-size: 16pt; color: #0066cc; font-weight: bold; text-align: center; border-bottom: 2px solid #0066cc; padding-bottom: 5px; margin: 0;'>
                            تصدير البيانات
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 10px;">
                        <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; margin: 0; text-align: justify;'>
                            يستخدم هذا الزر لتصدير بيانات التلاميذ المحددين إلى ملف Excel.
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #0066cc; font-weight: bold; border-bottom: 1px solid #0066cc; padding-bottom: 3px; margin: 0;'>
                            خطوات الاستخدام:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ol style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>حدد المستوى المطلوب من الجدول الأيمن.</li>
                            <li>حدد القسم المطلوب من الجدول الأوسط.</li>
                            <li>حدد التلميذ أو التلاميذ المطلوبين بالنقر على مربع الاختيار.</li>
                            <li>انقر على زر "تصدير" لفتح نافذة تصدير البيانات.</li>
                            <li>حدد الأعمدة المطلوب تصديرها.</li>
                            <li>انقر على زر "تصدير" في نافذة التصدير.</li>
                        </ol>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #0066cc; font-weight: bold; border-bottom: 1px solid #0066cc; padding-bottom: 3px; margin: 0;'>
                            ملاحظات هامة:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ul style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>يمكنك تحديد عدة تلاميذ في نفس الوقت لتصدير بياناتهم.</li>
                            <li>يمكنك اختيار الأعمدة التي ترغب في تصديرها.</li>
                            <li>سيتم حفظ الملف بتنسيق Excel (.xlsx).</li>
                        </ul>
                    </td>
                </tr>
            </table>
        </div>
        """
        # ملاحظة: تم نقل التصدير للقائمة المنسدلة

        # تلميح زر بطاقة اللوائح
        regulations_card_tooltip = """
        <div dir='rtl' style='text-align: right; direction: rtl; width: 400px; padding: 15px; background-color: #f0f8ff; border: 2px solid #0066cc; border-radius: 8px;'>
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td align="center">
                        <p style='font-family: Calibri; font-size: 16pt; color: #0066cc; font-weight: bold; text-align: center; border-bottom: 2px solid #0066cc; padding-bottom: 5px; margin: 0;'>
                            بطاقة اللوائح
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 10px;">
                        <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; margin: 0; text-align: justify;'>
                            يستخدم هذا الزر لعرض بطاقة اللوائح والإحصائيات للقسم المحدد، مع معلومات تفصيلية عن التلاميذ والحضور والغياب.
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #0066cc; font-weight: bold; border-bottom: 1px solid #0066cc; padding-bottom: 3px; margin: 0;'>
                            خطوات الاستخدام:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ol style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>حدد المستوى المطلوب من الجدول الأيمن.</li>
                            <li>حدد القسم المطلوب من الجدول الأوسط.</li>
                            <li>انقر على زر "بطاقة اللوائح" لعرض البطاقة التفصيلية للقسم.</li>
                        </ol>
                    </td>
                </tr>
                <tr>
                    <td align="right" style="padding-top: 15px;">
                        <p style='font-family: Calibri; font-size: 14pt; color: #0066cc; font-weight: bold; border-bottom: 1px solid #0066cc; padding-bottom: 3px; margin: 0;'>
                            ملاحظات هامة:
                        </p>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <ul style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold; padding-right: 25px; margin: 5px 0 0 0;'>
                            <li>لا يشترط تحديد أي تلميذ لاستخدام هذه الميزة، فقط حدد القسم المطلوب.</li>
                            <li>تعرض البطاقة معلومات إحصائية عن القسم مثل عدد التلاميذ وتوزيعهم.</li>
                            <li>يمكنك طباعة البطاقة أو حفظها كملف PDF من خلال النافذة.</li>
                            <li>تحتوي البطاقة على معلومات محدثة من قاعدة البيانات.</li>
                        </ul>
                    </td>
                </tr>
            </table>
        </div>
        """
        self.buttons["بطاقة اللوائح"].setToolTip(regulations_card_tooltip)

        # لا نضيف تلميح لزر التحديث

    def apply_styles(self):
        """تطبيق التنسيقات المتقدمة والجميلة - محسن من sub252_window.py"""
        qss = """
            /* النافذة الرئيسية مع تدرج لوني جميل */
            QWidget {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fc,
                    stop: 1 #e9ecef
                );
                color: #333;
                font-family: 'Calibri';
            }

            /* تنسيق التسميات */
            QLabel {
                padding: 5px;
                color: #2c3e50;
                font-weight: bold;
            }

            /* تنسيق القوائم المنسدلة والحقول */
            QComboBox, QDateEdit, QTimeEdit {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 8px 12px;
                min-height: 25px;
                font-size: 13pt;
                font-weight: bold;
            }

            QComboBox:hover, QDateEdit:hover, QTimeEdit:hover {
                border: 2px solid #3498db;
                background-color: #ecf0f1;
            }

            QComboBox:focus, QDateEdit:focus, QTimeEdit:focus {
                border: 2px solid #2980b9;
                background-color: #e8f4fd;
            }

            /* تنسيق القوائم المنسدلة المتقدم */
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 25px;
                border-left: 2px solid #bdc3c7;
                border-top-right-radius: 8px;
                border-bottom-right-radius: 8px;
                background-color: #ecf0f1;
            }

            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #2c3e50;
                margin-right: 5px;
            }

            QDateEdit::drop-down, QTimeEdit::drop-down {
                width: 25px;
                background-color: #ecf0f1;
                border-radius: 5px;
            }
            /* تنسيق الجداول المتقدم */
            QTableView {
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                gridline-color: #ecf0f1;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-color: #2c3e50;
                selection-background-color: #ffc107;
                font-size: 13pt;
            }

            /* رأس الجدول المحسن */
            QHeaderView::section {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db,
                    stop: 1 #2980b9
                );
                color: white;
                padding: 10px;
                border: 1px solid #2980b9;
                font-weight: bold;
                font-size: 14pt;
                text-align: center;
            }

            QHeaderView::section:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5dade2,
                    stop: 1 #3498db
                );
            }

            QHeaderView {
                border: none;
                border-radius: 5px;
            }

            /* خلايا الجدول */
            QTableView::item {
                padding: 8px;
                border: none;
                border-bottom: 1px solid #ecf0f1;
            }

            QTableView::item:selected {
                background-color: #ffc107;
                color: #2c3e50;
                font-weight: bold;
            }

            QTableView::item:hover {
                background-color: #e8f4fd;
                color: #2c3e50;
            }
            /* أشرطة التمرير المحسنة */
            QScrollBar:vertical {
                border: none;
                background: #ecf0f1;
                width: 15px;
                margin: 0px;
                border-radius: 7px;
            }

            QScrollBar::handle:vertical {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #3498db,
                    stop: 1 #2980b9
                );
                min-height: 25px;
                border-radius: 7px;
                margin: 2px;
            }

            QScrollBar::handle:vertical:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #5dade2,
                    stop: 1 #3498db
                );
            }

            QScrollBar:horizontal {
                border: none;
                background: #ecf0f1;
                height: 15px;
                margin: 0px;
                border-radius: 7px;
            }

            QScrollBar::handle:horizontal {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db,
                    stop: 1 #2980b9
                );
                min-width: 25px;
                border-radius: 7px;
                margin: 2px;
            }

            QScrollBar::handle:horizontal:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5dade2,
                    stop: 1 #3498db
                );
            }

            /* إخفاء أزرار أشرطة التمرير */
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical,
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                border: none;
                background: none;
                width: 0px;
                height: 0px;
            }
        """
        self.setStyleSheet(qss)

    # دالة موحدة لضبط مظهر الجداول
    def setup_table_appearance(self, table_view, column_widths=None):
        # تطبيق نمط موحد وعادي للجدول
        table_view.setFont(QFont("Calibri", 13, QFont.Bold)) # تقليل حجم الخط
        table_view.horizontalHeader().setFont(QFont("Calibri", 14))  # تقليل حجم خط العنوان
        table_view.horizontalHeader().setMinimumHeight(13)  # تقليل ارتفاع العنوان بمقدار الثلث إضافي (كان 19)
        table_view.verticalHeader().setDefaultSectionSize(30)  # ارتفاع مناسب للصفوف
        table_view.verticalHeader().setVisible(False)
        table_view.horizontalHeader().setStretchLastSection(True)

        # تعيين عرض الأعمدة إذا تم توفيرها
        if column_widths and len(column_widths) > 0:
            for col, width in enumerate(column_widths):
                if col < table_view.model().columnCount():
                    table_view.setColumnWidth(col, width)
                    table_view.horizontalHeader().setSectionResizeMode(col, QHeaderView.Fixed)

    def setup_lists_table_appearance(self):
        # تعديل العناوين لتشمل العمود الجديد
        headers = ["اختيار", "رت", "الرمز", "الاسم والنسب", "السماح", "التأخر", "المخالفات", "الهاتف الأول",  "ملاحظات"]
        widths = [50, 50, 130, 160, 80, 80, 80, 110, 200]

        # تطبيق العناوين على الجدول
        column_count = self.model_lists.columnCount()
        if (column_count <= 0): return

        for i in range(column_count):
            header_text = headers[i] if i < len(headers) else f"Col_{i}"
            self.model_lists.setHeaderData(i, Qt.Horizontal, header_text)

        # تطبيق إعدادات مظهر الجدول
        self.setup_table_appearance(self.table_lists, widths)

    def load_initial_data(self):
        """تحميل البيانات الأولية مع معالجة محسنة للأخطاء"""
        try:
            if not self.current_academic_year:
                print("تحذير: لم يتم تحديد السنة الدراسية")
                return

            print(f"تحميل البيانات للسنة الدراسية: {self.current_academic_year}")

            # التأكد من اتصال قاعدة البيانات
            if not self.db:
                print("خطأ: قاعدة البيانات غير متصلة")
                self.show_enhanced_message("error", "خطأ", "قاعدة البيانات غير متصلة")
                return

            # التأكد من وجود جدول اللوائح عند بدء تشغيل البرنامج
            regulations_exist = self.check_regulations_table()
            if not regulations_exist:
                print("تحذير: جدول اللوائح فارغ أو غير موجود")

            # تحميل بيانات الحراس والمستويات
            self.filter_levels_by_guard(self.guard_combo.currentText())

            print("تم تحميل البيانات الأولية بنجاح")

        except Exception as e:
            print(f"خطأ في تحميل البيانات الأولية: {e}")
            self.show_enhanced_message("error", "خطأ في تحميل البيانات",
                                     f"حدث خطأ أثناء تحميل البيانات الأساسية:\n{str(e)}")

    def update_levels_model(self, guard_filter=None):
        """تحديث نموذج المستويات مع معالجة محسنة للأخطاء"""
        try:
            print(f"تحديث نموذج المستويات للحارس: {guard_filter}")

            # التحقق من اتصال قاعدة البيانات
            if not self.db:
                print("خطأ: قاعدة البيانات غير متصلة")
                return

            if not self.current_academic_year:
                print("تحذير: لم يتم تحديد السنة الدراسية")
                return

            # التحقق من وجود جدول البنية التربوية
            from PyQt5.QtSql import QSqlQuery
            check_query = QSqlQuery(self.db)
            if not check_query.exec_("SELECT name FROM sqlite_master WHERE type='table' AND name='البنية_التربوية'"):
                print(f"خطأ في التحقق من جدول البنية التربوية: {check_query.lastError().text()}")
                return

            if not check_query.next():
                print("تحذير: جدول البنية_التربوية غير موجود")
                self.show_enhanced_message("warning", "تحذير", "جدول البنية التربوية غير موجود في قاعدة البيانات")
                return

            base_query = "SELECT DISTINCT المستوى FROM البنية_التربوية WHERE السنة_الدراسية = :year"
            conditions = []; params = {":year": self.current_academic_year}
            guard_column_name = "الأقسام_المسندة"
            if guard_filter and guard_filter.strip():
                 conditions.append(f"\"{guard_column_name}\" = :guard"); params[":guard"] = guard_filter
            query_str = base_query;
            if conditions: query_str += " AND " + " AND ".join(conditions)

            # تغيير الترتيب ليكون حسب ترتيب_المستويات بدلاً من المستوى
            query_str += " ORDER BY ترتيب_المستويات, المستوى;"

            query = QSqlQuery(db=self.db); query.prepare(query_str)
            for key, value in params.items(): query.bindValue(key, value)
            if query.exec_():
                self.model_levels.setQuery(query)
                if not self.model_levels.lastError().isValid():
                    self.model_levels.setHeaderData(0, Qt.Horizontal, "المستويات")
                    # تطبيق إعدادات مظهر جدول المستويات
                    self.setup_table_appearance(self.table_levels, [120])
                    self.selected_level = None; self.selected_class = None
                    self.model_classes.setQuery(QSqlQuery("SELECT '' WHERE 0=1", db=self.db))
                    self.model_lists.setQuery(QSqlQuery("SELECT '' WHERE 0=1", db=self.db))
                    print("تم تحديث نموذج المستويات بنجاح")
                else:
                    print(f"خطأ في نموذج المستويات: {self.model_levels.lastError().text()}")
            else:
                print(f"خطأ في تنفيذ استعلام المستويات: {query.lastError().text()}")

        except Exception as e:
            print(f"خطأ في تحديث نموذج المستويات: {e}")
        else:
            # محاولة بديلة في حالة عدم وجود عمود ترتيب_المستويات
            fallback_query = base_query
            if conditions: fallback_query += " AND " + " AND ".join(conditions)
            fallback_query += " ORDER BY المستوى;"

            fallback = QSqlQuery(db=self.db); fallback.prepare(fallback_query)
            for key, value in params.items(): fallback.bindValue(key, value)

            if fallback.exec_():
                self.model_levels.setQuery(fallback)
                if not self.model_levels.lastError().isValid():
                    self.model_levels.setHeaderData(0, Qt.Horizontal, "المستويات")
                    self.setup_table_appearance(self.table_levels, [120])
                    self.selected_level = None; self.selected_class = None
                    self.model_classes.setQuery(QSqlQuery("SELECT '' WHERE 0=1", db=self.db))
                    self.model_lists.setQuery(QSqlQuery("SELECT '' WHERE 0=1", db=self.db))

    def filter_levels_by_guard(self, selected_guard_text):
        self.update_levels_model(guard_filter=selected_guard_text)

    def update_classes_model(self):
        # التحقق من اتصال قاعدة البيانات
        if not self.db:
            print("خطأ: قاعدة البيانات غير متصلة")
            return

        if not self.selected_level:
            self.model_classes.setQuery(QSqlQuery("SELECT '' WHERE 0=1", db=self.db))
            self.selected_class = None
            self.update_lists_model()
            return

        # استعلام SQL المحسن للترتيب الرقمي للأقسام
        query_str = """
            WITH RECURSIVE
            extracted_numbers(id, القسم, num) AS (
                SELECT
                    rowid,
                    القسم,
                    CAST(
                        CASE
                            WHEN instr(القسم, '-') > 0 THEN
                                substr(القسم, instr(القسم, '-') + 1)
                            WHEN القسم GLOB '*[0-9]*' THEN
                                substr(القسم, length(القسم) - length(replace(replace(replace(replace(replace(replace(replace(replace(replace(replace(
                                    القسم, '0', ''), '1', ''), '2', ''), '3', ''), '4', ''), '5', ''), '6', ''), '7', ''), '8', ''), '9', '')))
                            ELSE '0'
                        END AS INTEGER
                    ) as number_part
                FROM البنية_التربوية
                WHERE السنة_الدراسية = :year AND المستوى = :level
            )
            SELECT DISTINCT orig.القسم
            FROM البنية_التربوية orig
            LEFT JOIN extracted_numbers en ON orig.القسم = en.القسم
            WHERE orig.السنة_الدراسية = :year
            AND orig.المستوى = :level
            ORDER BY
                CASE
                    WHEN orig.القسم GLOB '*[0-9]*' THEN 1
                    ELSE 2
                END,
                en.num,
                orig.القسم
        """

        query = QSqlQuery(db=self.db)
        query.prepare(query_str)
        query.bindValue(":year", self.current_academic_year)
        query.bindValue(":level", self.selected_level)

        if query.exec_():
            self.model_classes.setQuery(query)
            if not self.model_classes.lastError().isValid():
                self.model_classes.setHeaderData(0, Qt.Horizontal, "الأقسام")
                self.setup_table_appearance(self.table_classes, [120])
                self.selected_class = None
                self.update_lists_model()
        else:
            print(f"خطأ في تنفيذ استعلام الأقسام: {query.lastError().text()}")

            # استعلام بديل أبسط في حالة فشل الاستعلام المتقدم
            fallback_query = """
                SELECT DISTINCT القسم
                FROM البنية_التربوية
                WHERE السنة_الدراسية = :year
                AND المستوى = :level
                ORDER BY القسم
            """

            query_fallback = QSqlQuery(db=self.db)
            query_fallback.prepare(fallback_query)
            query_fallback.bindValue(":year", self.current_academic_year)
            query_fallback.bindValue(":level", self.selected_level)

            if query_fallback.exec_():
                self.model_classes.setQuery(query_fallback)
                self.model_classes.setHeaderData(0, Qt.Horizontal, "الأقسام")
                self.setup_table_appearance(self.table_classes, [120])
                self.selected_class = None
                self.update_lists_model()

                print("تم استخدام الترتيب الافتراضي للأقسام نتيجة فشل الترتيب الرقمي.")

    def check_regulations_table(self):
        """التحقق من وجود جدول اللوائح وما إذا كان يحتوي على بيانات"""
        try:
            print("التحقق من جدول اللوائح...")

            # التحقق من وجود جدول اللوائح باستخدام QSqlQuery
            from PyQt5.QtSql import QSqlQuery

            check_query = QSqlQuery(self.db)
            if not check_query.exec_("SELECT name FROM sqlite_master WHERE type='table' AND name='اللوائح'"):
                print(f"خطأ في استعلام جداول قاعدة البيانات: {check_query.lastError().text()}")
                return False

            if not check_query.next():
                print("جدول اللوائح غير موجود، سيتم إنشاؤه...")
                # إنشاء جدول اللوائح إذا لم يكن موجوداً
                create_query = QSqlQuery(self.db)
                if not create_query.exec_("""
                    CREATE TABLE IF NOT EXISTS 'اللوائح' (
                        'السنة_الدراسية' TEXT,
                        'القسم' TEXT,
                        'المستوى' TEXT,
                        'الرمز' TEXT,
                        'رت' TEXT,
                        'مجموع التلاميذ' INTEGER DEFAULT 0,
                        PRIMARY KEY('السنة_الدراسية', 'الرمز')
                    )
                """):
                    print(f"خطأ في إنشاء جدول اللوائح: {create_query.lastError().text()}")
                    return False
                print("تم إنشاء جدول اللوائح بنجاح")
                return False  # الجدول فارغ لأنه تم إنشاؤه للتو

            # التحقق مما إذا كان الجدول يحتوي على بيانات
            data_query = QSqlQuery(self.db)
            if not data_query.exec_("SELECT COUNT(*) FROM اللوائح"):
                print(f"خطأ في عد بيانات جدول اللوائح: {data_query.lastError().text()}")
                return False

            if data_query.next():
                count = data_query.value(0)
                print(f"جدول اللوائح يحتوي على {count} سجل")
                if count > 0:
                    return True  # الجدول موجود ويحتوي على بيانات
                else:
                    print("جدول اللوائح موجود ولكنه فارغ")
                    return False  # الجدول موجود ولكنه فارغ
            else:
                print("لم يتم الحصول على عدد السجلات من جدول اللوائح")
                return False

        except Exception as e:
            print(f"خطأ في التحقق من وجود جدول اللوائح: {e}")
            return False

    def update_lists_model(self):
        # التحقق من اتصال قاعدة البيانات
        if not self.db:
            print("خطأ: قاعدة البيانات غير متصلة")
            return

        # مسح تحديدات مربعات الاختيار عند تغيير القسم
        if hasattr(self, 'checkbox_delegate') and self.checkbox_delegate:
            self.checkbox_delegate.checked_items.clear()
            # تحديث عرض الجدول لإظهار التغييرات
            if hasattr(self, 'table_lists') and self.table_lists:
                self.table_lists.viewport().update()

        if not self.current_academic_year or not self.selected_class:
            self.model_lists.setQuery(QSqlQuery("SELECT '' WHERE 0=1", db=self.db))
            return

        # التحقق من وجود بيانات في جدول اللوائح
        has_data = self.check_regulations_table()
        if not has_data:
            # عرض رسالة تنبيه محسنة للمستخدم
            self.show_enhanced_message("warning", "تنبيه",
                "جدول اللوائح فارغ. يرجى استيراد البيانات أولاً من خلال نافذة استيراد البيانات.")
            self.model_lists.setQuery(QSqlQuery("SELECT '' WHERE 0=1", db=self.db))
            return

        # التحقق من وجود بيانات في جدول اللوائح للسنة الدراسية والقسم المحددين
        check_query = QSqlQuery(db=self.db)
        check_query.prepare("SELECT COUNT(*) FROM اللوائح WHERE السنة_الدراسية = :year AND القسم = :class")
        check_query.bindValue(":year", self.current_academic_year)
        check_query.bindValue(":class", self.selected_class)

        if check_query.exec_() and check_query.next() and check_query.value(0) == 0:
            # لا توجد بيانات في جدول اللوائح للسنة الدراسية والقسم المحددين
            self.model_lists.setQuery(QSqlQuery("SELECT '' WHERE 0=1", db=self.db))
            return

        # محاولة تنفيذ الاستعلام مع معالجة الأخطاء المحتملة
        try:
            query_str = """
                SELECT
                    0 as checkbox,  -- عمود مربع الاختيار
                    ROW_NUMBER() OVER (ORDER BY CAST(COALESCE(l.رت, '0') AS INTEGER)) as رت,
                    s.الرمز,
                    s.الاسم_والنسب,
                    s.السماح,
                    s.التأخر,
                    s.عدد_المخالفات,
                    s.الهاتف_الأول,
                    s.ملاحظات
                FROM اللوائح l
                JOIN السجل_العام s ON l.الرمز = s.الرمز
                WHERE l.السنة_الدراسية = :year
                AND l.القسم = :class
                ORDER BY CAST(COALESCE(l.رت, '0') AS INTEGER);
            """
            query = QSqlQuery(db=self.db)
            query.prepare(query_str)
            query.bindValue(":year", self.current_academic_year)
            query.bindValue(":class", self.selected_class)

            if query.exec_():
                self.model_lists.setQuery(query)
                if not self.model_lists.lastError().isValid():
                    # إضافة مندوب مربع الاختيار للعمود الأول
                    self.table_lists.setItemDelegateForColumn(self.checkbox_column_index, self.checkbox_delegate)
                    self.setup_lists_table_appearance()

                    # تمكين التفاعل مع مربعات الاختيار
                    self.table_lists.setEditTriggers(QAbstractItemView.NoEditTriggers)
                    self.table_lists.viewport().update()  # تحديث العرض
                else:
                    print(f"خطأ في استعلام اللوائح: {self.model_lists.lastError().text()}")
            else:
                print(f"فشل تنفيذ استعلام اللوائح: {query.lastError().text()}")

        except Exception as e:
            print(f"خطأ غير متوقع في تحديث جدول اللوائح: {e}")
            self.model_lists.setQuery(QSqlQuery("SELECT '' WHERE 0=1", db=self.db))

    @pyqtSlot(QItemSelection, QItemSelection)
    def on_level_selected(self, selected, _):  # تغيير اسم المعامل لتجنب تحذير عدم الاستخدام
        indexes = selected.indexes()
        if indexes: self.selected_level = self.model_levels.data(indexes[0], Qt.DisplayRole)
        else: self.selected_level = None

        # مسح تحديدات مربعات الاختيار عند تغيير المستوى
        if hasattr(self, 'checkbox_delegate') and self.checkbox_delegate:
            self.checkbox_delegate.checked_items.clear()
            # تحديث عرض الجدول لإظهار التغييرات
            if hasattr(self, 'table_lists') and self.table_lists:
                self.table_lists.viewport().update()

        self.update_classes_model()

    @pyqtSlot(QItemSelection, QItemSelection)
    def on_class_selected(self, selected, _):  # تغيير اسم المعامل لتجنب تحذير عدم الاستخدام
        indexes = selected.indexes()
        if indexes: self.selected_class = self.model_classes.data(indexes[0], Qt.DisplayRole)
        else: self.selected_class = None
        self.update_lists_model()

    def export_selected_students(self):
        """تصدير بيانات التلاميذ المحددين إلى ملف Excel أو Word"""
        # التحقق من وجود تلاميذ محددين في القسم الحالي
        checked_rows = self.checkbox_delegate.get_checked_rows()

        # جمع رموز التلاميذ المحددين في القسم الحالي
        current_selected_codes = []
        for row in checked_rows:
            code_index = self.model_lists.index(row, 2)  # الرمز في العمود 2
            code = self.model_lists.data(code_index, Qt.DisplayRole)
            if code:
                current_selected_codes.append(code)
                # إضافة الرموز إلى قائمة جميع التلاميذ المحددين
                self.all_selected_students.add(code)

        # إذا تم تحديد تلاميذ في القسم الحالي، عرض رسالة تأكيد مع تفاصيل التلاميذ المحددين من كل قسم
        if current_selected_codes:
            # جمع معلومات التلاميذ المحددين مرتبة حسب القسم
            selected_by_class = {}

            # استعلام للحصول على معلومات التلاميذ المحددين
            query = QSqlQuery(db=self.db)
            query_str = """
                SELECT l.القسم, COUNT(*) as count
                FROM اللوائح l
                WHERE l.الرمز IN ({}) AND l.السنة_الدراسية = :year
                GROUP BY l.القسم
                ORDER BY
                    CASE
                        WHEN l.القسم GLOB '*[0-9]*' THEN 1
                        ELSE 2
                    END,
                    CAST(
                        CASE
                            WHEN instr(l.القسم, '-') > 0 THEN
                                substr(l.القسم, instr(l.القسم, '-') + 1)
                            WHEN l.القسم GLOB '*[0-9]*' THEN
                                substr(l.القسم, length(l.القسم) - length(replace(replace(replace(replace(replace(replace(replace(replace(replace(replace(
                                    l.القسم, '0', ''), '1', ''), '2', ''), '3', ''), '4', ''), '5', ''), '6', ''), '7', ''), '8', ''), '9', '')))
                            ELSE '0'
                        END AS INTEGER
                    ),
                    l.القسم
            """.format(", ".join([f"'{code}'" for code in self.all_selected_students]))

            query.prepare(query_str)
            query.bindValue(":year", self.current_academic_year)

            if query.exec_():
                while query.next():
                    class_name = query.value(0)
                    count = query.value(1)
                    selected_by_class[class_name] = count

            # إنشاء رسالة مفصلة
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("تم التحديد")
            msg_box.setIcon(QMessageBox.Information)

            # تعيين أيقونة البرنامج
            msg_box.setWindowIcon(QIcon("01.ico"))

            # إنشاء نص الرسالة بتنسيق HTML
            message = f"""
            <html>
            <head>
                <style>
                    body {{ font-family: Calibri; }}
                    h2 {{ color: #0066cc; font-size: 14pt; margin-bottom: 10px; }}
                    .details {{ font-weight: bold; font-size: 13pt; }}
                    .total {{ color: #0066cc; font-weight: bold; font-size: 14pt; margin-top: 15px; }}
                </style>
            </head>
            <body>
                <h2>تم تحديد التلاميذ من الأقسام التالية:</h2>
                <div class="details">
            """

            # إضافة تفاصيل كل قسم
            for class_name, count in selected_by_class.items():
                message += f"• القسم {class_name}: {count} تلميذ<br>"

            # إضافة المجموع الكلي
            message += f"""
                </div>
                <div class="total">
                    المجموع الكلي للتلاميذ المحددين: {len(self.all_selected_students)}
                </div>
            </body>
            </html>
            """

            msg_box.setText(message)

            # تعيين أحجام الأزرار وتنسيقها
            ok_button = msg_box.addButton(QMessageBox.Ok)
            ok_button.setText("موافق")
            ok_button.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    font-weight: bold;
                    border-radius: 5px;
                    padding: 8px 16px;
                    min-width: 100px;
                    min-height: 30px;
                    font-family: Calibri;
                    font-size: 13pt;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """)

            msg_box.exec_()

        # التحقق من وجود تلاميذ محددين في جميع الأقسام
        if not self.all_selected_students:
            self.show_styled_message_box(
                title="تنبيه",
                message="يرجى تحديد تلميذ واحد على الأقل للتصدير",
                icon=QMessageBox.Warning,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )
            return

        # فتح نافذة التصدير باستخدام جميع التلاميذ المحددين
        export_window = ExportWindow(
            parent=self,
            selected_students=list(self.all_selected_students),
            db=self.db,
            academic_year=self.current_academic_year
        )
        export_window.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه النص من اليمين إلى اليسار

        # عند إغلاق نافذة التصدير، مسح قائمة التلاميذ المحددين
        result = export_window.exec_()
        if result == QDialog.Accepted:
            # مسح قائمة التلاميذ المحددين بعد التصدير الناجح
            self.all_selected_students.clear()
            # مسح تحديدات مربعات الاختيار
            self.checkbox_delegate.checked_items.clear()
            self.table_lists.viewport().update()

    @pyqtSlot(QModelIndex)
    def on_table_cell_clicked(self, index, column=None):
        # Handle both calling conventions:
        # 1. Single QModelIndex parameter (original)
        # 2. Separate row and column parameters (from EnhancedSimpleSearchWindow)
        if column is None:
            # Original calling convention with QModelIndex
            row = index.row()
            column = index.column()
        else:
            # Called with separate row and column parameters
            row = index

        # تعديل الشرط للتحقق من عمود الرمز (العمود 2) بدلاً من العمود 1
        if column == 2:  # column 2 is 'الرمز'
            model = self.table_lists.model()
            if model:
                student_code = model.data(model.index(row, column), Qt.DisplayRole)
                if student_code:
                    # نسخ الرمز إلى الحافظة
                    try:
                        from PyQt5.QtWidgets import QApplication
                        clipboard = QApplication.clipboard()
                        clipboard.setText(str(student_code))

                        # عرض رسالة تأكيد منسقة
                        self.show_styled_message_box(
                            title="تم النسخ",
                            message=f"تم نسخ رمز التلميذ إلى الحافظة:\n{student_code}",
                            icon=QMessageBox.Information,
                            button_color="#4CAF50",
                            button_hover_color="#45a049"
                        )

                        print(f"تم نسخ رمز التلميذ إلى الحافظة: {student_code}")

                    except Exception as e:
                        # عرض رسالة خطأ في حالة فشل النسخ
                        self.show_styled_message_box(
                            title="خطأ",
                            message=f"فشل في نسخ الرمز إلى الحافظة:\n{str(e)}",
                            icon=QMessageBox.Critical,
                            button_color="#f44336",
                            button_hover_color="#d32f2f"
                        )
                        print(f"خطأ في نسخ الرمز إلى الحافظة: {e}")
                else:
                    pass
            else:
                pass
        else:
            pass

    def get_selected_students_info(self):
        """الحصول على معلومات التلاميذ المحددين باستخدام مربعات الاختيار"""
        selected_info = []
        model = self.table_lists.model()
        checked_rows = self.checkbox_delegate.get_checked_rows()

        # ترتيب الصفوف المحددة
        sorted_rows = sorted(checked_rows)

        for i, row in enumerate(sorted_rows, 1):
            try:
                # لم نعد بحاجة إلى استخدام عمود رت لأننا نستخدم الترتيب التسلسلي
                code_index = model.index(row, 2)  # الرمز في العمود 2
                name_index = model.index(row, 3)  # الاسم في العمود 3

                code = model.data(code_index, Qt.DisplayRole)
                name = model.data(name_index, Qt.DisplayRole)

                code_str = str(code) if code is not None else ""
                name_str = str(name) if name is not None else ""

                if code_str:
                    # استخدام الترتيب التسلسلي بدلاً من القيمة المخزنة في عمود رت
                    selected_info.append({
                        'rt': str(i),  # استخدام الترتيب التسلسلي
                        'rt_int': i,   # استخدام الترتيب التسلسلي كرقم
                        'code': code_str,
                        'name': name_str,
                        'academic_year': self.current_academic_year  # إضافة السنة الدراسية
                    })
            except Exception as e:
                print(f"خطأ في استخراج بيانات الصف {row}: {e}")

        return selected_info



    # تم إزالة دالة clear_all_selections

    def get_selected_section(self): return self.selected_class if self.selected_class else ""
    def refresh_regulations_table(self):
        # التحقق من اتصال قاعدة البيانات
        if not self.db:
            print("خطأ: قاعدة البيانات غير متصلة")
            return
        self.update_lists_model()
    def get_current_school_year_and_semester(self):
        year = self.current_academic_year if self.current_academic_year else "غير محدد"; semester = "الأول"; query = QSqlQuery(db=self.db)
        if query.exec_("SELECT الأسدس FROM بيانات_المؤسسة LIMIT 1") and query.next(): fetched_semester = query.value(0);
        if fetched_semester: semester = fetched_semester
        return year, semester

    def update_data(self):
        """تحديث أعمدة السماح والتأخر وعدد_المخالفات في جدول السجل_العام"""
        try:
            # الحصول على السنة الدراسية والأسدس من جدول بيانات_المؤسسة
            school_year_query = QSqlQuery(db=self.db)
            school_year_query.exec_("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            current_year = ""
            current_semester = ""
            if school_year_query.next():
                current_year = school_year_query.value(0)
                current_semester = school_year_query.value(1)

            # إعداد استعلام تحديث عمود السماح
            update_entry_query = QSqlQuery(db=self.db)
            update_entry_prepared = update_entry_query.prepare("""
                UPDATE السجل_العام
                SET السماح = (
                    SELECT COUNT(*)
                    FROM ورقة_السماح_بالدخول
                    WHERE ورقة_السماح_بالدخول.الرمز = السجل_العام.الرمز
                    AND ورقة_السماح_بالدخول.ورقة_السماح = ' سماح '
                    AND ورقة_السماح_بالدخول.السنة_الدراسية = :year
                    AND ورقة_السماح_بالدخول.الأسدس = :semester
                )
            """)
            update_entry_query.bindValue(":year", current_year)
            update_entry_query.bindValue(":semester", current_semester)

            # إعداد استعلام تحديث عمود التأخر
            update_late_query = QSqlQuery(db=self.db)
            update_late_prepared = update_late_query.prepare("""
                UPDATE السجل_العام
                SET التأخر = (
                    SELECT COUNT(*)
                    FROM ورقة_السماح_بالدخول
                    WHERE ورقة_السماح_بالدخول.الرمز = السجل_العام.الرمز
                    AND ورقة_السماح_بالدخول.ورقة_السماح = ' تأخر '
                    AND ورقة_السماح_بالدخول.السنة_الدراسية = :year
                    AND ورقة_السماح_بالدخول.الأسدس = :semester
                )
            """)
            update_late_query.bindValue(":year", current_year)
            update_late_query.bindValue(":semester", current_semester)

            # إعداد استعلام تحديث عمود عدد_المخالفات
            update_violations_query = QSqlQuery(db=self.db)
            update_violations_prepared = update_violations_query.prepare("""
                UPDATE السجل_العام
                SET عدد_المخالفات = (
                    SELECT COUNT(*)
                    FROM المخالفات
                    WHERE المخالفات.رمز_التلميذ = السجل_العام.الرمز
                    AND المخالفات.السنة_الدراسية = :year
                    AND المخالفات.الأسدس = :semester
                )
            """)
            update_violations_query.bindValue(":year", current_year)
            update_violations_query.bindValue(":semester", current_semester)

            if not update_entry_prepared or not update_late_prepared or not update_violations_prepared:
                self.show_styled_message_box(
                    title="خطأ",
                    message="فشل في إعداد استعلامات قاعدة البيانات",
                    icon=QMessageBox.Critical,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
                return

            # بدء المعاملة
            self.db.transaction()

            try:
                # تنفيذ استعلامات التحديث
                entry_success = update_entry_query.exec_()
                late_success = update_late_query.exec_()
                violations_success = update_violations_query.exec_()

                if entry_success and late_success and violations_success:
                    self.db.commit()

                    # تحديث جدول اللوائح
                    self.refresh_regulations_table()

                    # عرض رسالة نجاح
                    self.show_custom_success_message(
                        "تم تحديث البيانات بنجاح:\n"
                        "1. تم تحديث عمود السماح\n"
                        "2. تم تحديث عمود التأخر\n"
                        "3. تم تحديث عمود عدد_المخالفات",
                        "تم التحديث بنجاح"
                    )
                else:
                    self.db.rollback()
                    error_messages = []
                    if not entry_success:
                        error_messages.append(f"خطأ في تحديث عمود السماح: {update_entry_query.lastError().text()}")
                    if not late_success:
                        error_messages.append(f"خطأ في تحديث عمود التأخر: {update_late_query.lastError().text()}")
                    if not violations_success:
                        error_messages.append(f"خطأ في تحديث عمود عدد_المخالفات: {update_violations_query.lastError().text()}")

                    self.show_styled_message_box(
                        title="خطأ",
                        message="\n".join(error_messages),
                        icon=QMessageBox.Critical,
                        button_color="#f44336",
                        button_hover_color="#d32f2f"
                    )

            except Exception as e:
                self.db.rollback()
                self.show_styled_message_box(
                    title="خطأ",
                    message=f"حدث خطأ أثناء تحديث البيانات:\n{str(e)}",
                    icon=QMessageBox.Critical,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )

        except Exception as e:
            self.show_styled_message_box(
                title="خطأ",
                message=f"حدث خطأ غير متوقع:\n{str(e)}",
                icon=QMessageBox.Critical,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )

    def add_to_entry_sheet(self):
        selected_students=self.get_selected_students_info()
        section=self.get_selected_section()
        if not selected_students:
            self.show_styled_message_box(
                title="تنبيه",
                message="الرجاء تحديد تلميذ واحد على الأقل",
                icon=QMessageBox.Information,
                button_color="#0066cc",
                button_hover_color="#0055bb"
            )
            return
        if not section:
            self.show_styled_message_box(
                title="تنبيه",
                message="الرجاء تحديد قسم أولاً.",
                icon=QMessageBox.Warning,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )
            return

        # استخدام نافذة التأكيد المخصصة من وحدة sub100_window إذا كانت متوفرة
        if CUSTOM_DIALOGS_IMPORTED:
            # استخدام الدالة المخصصة من وحدة sub100_window
            confirm_dialog = ConfirmationDialogs.create_entry_confirmation_dialog(self, selected_students)
        else:
            # استخدام النافذة الافتراضية في حالة عدم توفر وحدة sub100_window
            confirm_dialog = QDialog(self)
            confirm_dialog.setWindowTitle("تأكيد السماح بالدخول")
            confirm_dialog.setFixedSize(500, 400)
            confirm_dialog.setLayoutDirection(Qt.RightToLeft)

            # إضافة أيقونة البرنامج
            try:
                app_icon = QIcon("01.ico")
                confirm_dialog.setWindowIcon(app_icon)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج: {e}")

            # تنسيق النافذة
            confirm_dialog.setStyleSheet("""
                QDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #0066cc;
                    border-radius: 10px;
                }
                QLabel {
                    color: #333333;
                    font-weight: bold;
                }
                QTextBrowser {
                    border: 1px solid #3498db;
                    border-radius: 5px;
                    padding: 10px;
                    background-color: white;
                }
                QPushButton {
                    border-radius: 5px;
                    padding: 8px 15px;
                    font-weight: bold;
                    min-height: 35px;
                }
                QPushButton#confirm_btn {
                    background-color: #27ae60;
                    color: white;
                }
                QPushButton#confirm_btn:hover {
                    background-color: #2ecc71;
                    border: 2px solid #27ae60;
                }
                QPushButton#cancel_btn {
                    background-color: #e74c3c;
                    color: white;
                }
                QPushButton#cancel_btn:hover {
                    background-color: #c0392b;
                    border: 2px solid #e74c3c;
                }
            """)

            # إنشاء تخطيط النافذة
            layout = QVBoxLayout(confirm_dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إضافة أيقونة وعنوان
            header_layout = QHBoxLayout()

            # محاولة إضافة أيقونة البرنامج
            icon_label = QLabel()
            icon_label.setAlignment(Qt.AlignCenter)
            try:
                program_icon = QPixmap("01.ico")
                if not program_icon.isNull():
                    program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    icon_label.setPixmap(program_icon)
                    header_layout.addWidget(icon_label)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

            # إضافة عنوان النافذة
            title_label = QLabel("تأكيد السماح بالدخول")
            title_label.setFont(QFont("Calibri", 16, QFont.Bold))
            title_label.setStyleSheet("color: #0066cc;")
            title_label.setAlignment(Qt.AlignCenter)
            header_layout.addWidget(title_label, 1)

            layout.addLayout(header_layout)

            # إضافة رسالة التأكيد
            message_browser = QTextBrowser()
            message_browser.setReadOnly(True)

            # تنسيق النص بخط Calibri 13 أسود غامق وعرض المعلومات في جدول
            html_content = """
            <div dir='rtl' style='text-align: right;'>
                <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                    سيتم تسجيل السماح بالدخول للتلاميذ التالية أسماؤهم:
                </p>

                <table style='width: 100%; border-collapse: collapse; margin-top: 10px; font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                    <tr style='background-color: #0066cc; color: white;'>
                        <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>ر.ت</th>
                        <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>الرمز</th>
                        <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: right;'>الاسم والنسب</th>
                    </tr>
            """

            # إضافة معلومات التلاميذ في جدول مع ترتيب الأرقام الترتيبية بشكل صحيح
            for i, student in enumerate(selected_students, 1):
                html_content += f"""
                    <tr style='border-bottom: 1px solid #dddddd;'>
                        <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{i}</td>
                        <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{student['code']}</td>
                        <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: right;'>{student['name']}</td>
                    </tr>
                """

            html_content += """
                </table>
            </div>
            """

            message_browser.setHtml(html_content)
            layout.addWidget(message_browser)

            # إضافة أزرار التأكيد والإلغاء
            buttons_layout = QHBoxLayout()

            confirm_btn = QPushButton("تأكيد")
            confirm_btn.setObjectName("confirm_btn")
            confirm_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            confirm_btn.setCursor(Qt.PointingHandCursor)
            confirm_btn.setFixedWidth(105)  # تعديل العرض إلى 105
            confirm_btn.clicked.connect(confirm_dialog.accept)

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setObjectName("cancel_btn")
            cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            cancel_btn.setCursor(Qt.PointingHandCursor)
            cancel_btn.setFixedWidth(105)  # تعديل العرض إلى 105
            cancel_btn.clicked.connect(confirm_dialog.reject)

            buttons_layout.addWidget(confirm_btn)
            buttons_layout.addWidget(cancel_btn)

            layout.addLayout(buttons_layout)

        # عرض النافذة وانتظار النتيجة
        reply = confirm_dialog.exec_() == QDialog.Accepted
        if not reply:
            return

        # تحديد التاريخ والوقت حسب الإعدادات
        if not self.use_custom_datetime:
            # استخدام الوقت الحالي
            current_date = QDate.currentDate().toString("yyyy-MM-dd")
            time_to_use = QTime.currentTime().toString("hh:mm")
        else:
            # استخدام الوقت المخصص
            current_date = self.current_date.toString("yyyy-MM-dd")
            time_to_use = self.current_time.toString("hh:mm")

        year,semester=self.get_current_school_year_and_semester()

        try:
            # استخدام الطباعة الحرارية بتقنية تحويل النص إلى صورة
            if print_entry_form_direct:
                print_success = print_entry_form_direct(
                    students=selected_students,
                    section=section,
                    date_str=current_date,
                    time_str=time_to_use
                )
                if not print_success:
                    self.show_styled_message_box(
                        title="تنبيه",
                        message="فشلت الطباعة. تأكد من توصيل الطابعة الحرارية وتشغيلها.",
                        icon=QMessageBox.Warning,
                        button_color="#f44336",
                        button_hover_color="#d32f2f"
                    )
                    print_success = False
            else:
                # إذا كانت وحدة الطباعة بالملف النصي غير متوفرة
                self.show_styled_message_box(
                    title="تنبيه",
                    message="وحدة الطباعة باستخدام الملف النصي غير متوفرة",
                    icon=QMessageBox.Warning,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
                print_success = False

            if not print_success:
                self.show_styled_message_box(
                    title="تنبيه",
                    message="حدث خطأ أثناء طباعة ورقة الدخول",
                    icon=QMessageBox.Warning,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
        except Exception as e:
            print(f"خطأ في طباعة ورقة الدخول: {e}")
            self.show_styled_message_box(
                title="خطأ",
                message=f"حدث خطأ أثناء طباعة ورقة الدخول:\n{str(e)}",
                icon=QMessageBox.Critical,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )

        # متابعة تنفيذ العمليات الأصلية
        query_insert=QSqlQuery(db=self.db)
        query_update=QSqlQuery(db=self.db)
        success_count=0

        insert_prepared=query_insert.prepare("""
            INSERT INTO ورقة_السماح_بالدخول(
                السنة_الدراسية,
                الأسدس,
                الرقم_الترتيبي,
                التاريخ,
                الاسم_والنسب,
                سماح,
                الرمز,
                الوقت,
                ورقة_السماح
            ) VALUES (
                :year,
                :sem,
                (SELECT رت FROM اللوائح WHERE الرمز = :code AND السنة_الدراسية = :year),
                :date,
                :name,
                1,
                :code,
                :time,
                ' سماح '
            )
        """)

        update_prepared = query_update.prepare("""
            UPDATE السجل_العام
            SET السماح = (
                SELECT COUNT(*)
                FROM ورقة_السماح_بالدخول w
                WHERE w.الرمز = :code
                AND w.سماح = 1
                AND w.السنة_الدراسية = (SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1)
                AND w.الأسدس = (SELECT الأسدس FROM بيانات_المؤسسة LIMIT 1)
            )
            WHERE الرمز = :code
        """)

        if not insert_prepared: return
        if not update_prepared: return
        self.db.transaction()
        try:
            for student in selected_students:
                query_insert.bindValue(":year",year)
                query_insert.bindValue(":sem",semester)
                # لم نعد بحاجة إلى ربط الرقم الترتيبي هنا لأننا نستخدم استعلام فرعي
                # query_insert.bindValue(":rt",student['rt'])
                query_insert.bindValue(":date",current_date)
                query_insert.bindValue(":name",student['name'])
                query_insert.bindValue(":code",student['code'])
                query_insert.bindValue(":time",time_to_use)
                if query_insert.exec_():
                    query_update.bindValue(":code",student['code'])
                if not query_update.exec_():
                    pass
                success_count+=1
            self.db.commit()

            # مسح تحديدات مربعات الاختيار بعد النجاح
            self.checkbox_delegate.checked_items.clear()
            self.table_lists.viewport().update()

        except Exception as e:
            self.db.rollback()
            self.show_enhanced_message("error", "خطأ", f"حدث خطأ:\n{e}")
            return

        self.refresh_regulations_table()
        self.table_lists.clearSelection()

        # عرض رسالة نجاح مخصصة
        student_count = len(selected_students)
        if student_count > 0:
            if student_count == 1:
                message = f"تم تسجيل السماح بالدخول للتلميذ(ة) {selected_students[0]['name']} بنجاح"
            else:
                message = f"تم تسجيل السماح بالدخول لـ {student_count} تلاميذ بنجاح"

            self.show_custom_success_message(message, "تم التسجيل بنجاح")

    def add_to_late_sheet(self):
        selected_students=self.get_selected_students_info(); section=self.get_selected_section()
        if not selected_students:
            self.show_styled_message_box(
                title="تنبيه",
                message="الرجاء تحديد تلميذ واحد على الأقل",
                icon=QMessageBox.Information,
                button_color="#0066cc",
                button_hover_color="#0055bb"
            )
            return
        if not section:
            self.show_styled_message_box(
                title="تنبيه",
                message="الرجاء تحديد قسم أولاً.",
                icon=QMessageBox.Warning,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )
            return

        # استخدام نافذة التأكيد المخصصة من وحدة sub100_window إذا كانت متوفرة
        if CUSTOM_DIALOGS_IMPORTED:
            # استخدام الدالة المخصصة من وحدة sub100_window
            confirm_dialog = ConfirmationDialogs.create_late_confirmation_dialog(self, selected_students)
        else:
            # إنشاء نافذة تأكيد مخصصة
            confirm_dialog = QDialog(self)
            confirm_dialog.setWindowTitle("تأكيد تسجيل التأخر")
            confirm_dialog.setFixedSize(500, 400)
            confirm_dialog.setLayoutDirection(Qt.RightToLeft)

            # إضافة أيقونة البرنامج
            try:
                app_icon = QIcon("01.ico")
                confirm_dialog.setWindowIcon(app_icon)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج: {e}")

            # تنسيق النافذة
            confirm_dialog.setStyleSheet("""
                QDialog {
                    background-color: #f0f8ff;
                    border: 2px solid #0066cc;
                    border-radius: 10px;
                }
                QLabel {
                    color: #333333;
                    font-weight: bold;
                }
                QTextBrowser {
                    border: 1px solid #3498db;
                    border-radius: 5px;
                    padding: 10px;
                    background-color: white;
                }
                QPushButton {
                    border-radius: 5px;
                    padding: 8px 15px;
                    font-weight: bold;
                    min-height: 35px;
                }
                QPushButton#confirm_btn {
                    background-color: #27ae60;
                    color: white;
                }
                QPushButton#confirm_btn:hover {
                    background-color: #2ecc71;
                    border: 2px solid #27ae60;
                }
                QPushButton#cancel_btn {
                    background-color: #e74c3c;
                    color: white;
                }
                QPushButton#cancel_btn:hover {
                    background-color: #c0392b;
                    border: 2px solid #e74c3c;
                }
            """)

            # إنشاء تخطيط النافذة
            layout = QVBoxLayout(confirm_dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)

            # إضافة أيقونة وعنوان
            header_layout = QHBoxLayout()

            # محاولة إضافة أيقونة البرنامج
            icon_label = QLabel()
            icon_label.setAlignment(Qt.AlignCenter)
            try:
                program_icon = QPixmap("01.ico")
                if not program_icon.isNull():
                    program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    icon_label.setPixmap(program_icon)
                    header_layout.addWidget(icon_label)
            except Exception as e:
                print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

            # إضافة عنوان النافذة
            title_label = QLabel("تأكيد تسجيل التأخر")
            title_label.setFont(QFont("Calibri", 16, QFont.Bold))
            title_label.setStyleSheet("color: #0066cc;")
            title_label.setAlignment(Qt.AlignCenter)
            header_layout.addWidget(title_label, 1)

            layout.addLayout(header_layout)

            # إضافة رسالة التأكيد
            message_browser = QTextBrowser()
            message_browser.setReadOnly(True)

            # تنسيق النص بخط Calibri 13 أسود غامق وعرض المعلومات في جدول
            html_content = """
            <div dir='rtl' style='text-align: right;'>
                <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                    سيتم تسجيل التأخر للتلاميذ التالية أسماؤهم:
                </p>

                <table style='width: 100%; border-collapse: collapse; margin-top: 10px; font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                    <tr style='background-color: #0066cc; color: white;'>
                        <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>ر.ت</th>
                        <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>الرمز</th>
                        <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: right;'>الاسم والنسب</th>
                    </tr>
            """

            # إضافة معلومات التلاميذ في جدول مع ترتيب الأرقام الترتيبية بشكل صحيح
            for i, student in enumerate(selected_students, 1):
                html_content += f"""
                    <tr style='border-bottom: 1px solid #dddddd;'>
                        <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{i}</td>
                        <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{student['code']}</td>
                        <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: right;'>{student['name']}</td>
                    </tr>
                """

            html_content += """
                </table>
            </div>
            """

            message_browser.setHtml(html_content)
            layout.addWidget(message_browser)

            # إضافة أزرار التأكيد والإلغاء
            buttons_layout = QHBoxLayout()

            confirm_btn = QPushButton("تأكيد")
            confirm_btn.setObjectName("confirm_btn")
            confirm_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            confirm_btn.setCursor(Qt.PointingHandCursor)
            confirm_btn.setFixedWidth(105)  # تعديل العرض إلى 105
            confirm_btn.clicked.connect(confirm_dialog.accept)

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setObjectName("cancel_btn")
            cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
            cancel_btn.setCursor(Qt.PointingHandCursor)
            cancel_btn.setFixedWidth(105)  # تعديل العرض إلى 105
            cancel_btn.clicked.connect(confirm_dialog.reject)

            buttons_layout.addWidget(confirm_btn)
            buttons_layout.addWidget(cancel_btn)

            layout.addLayout(buttons_layout)

        # عرض النافذة وانتظار النتيجة
        reply = confirm_dialog.exec_() == QDialog.Accepted
        if not reply: return

        # تحديد التاريخ والوقت حسب الإعدادات
        if not self.use_custom_datetime:
            # استخدام الوقت الحالي
            current_date = QDate.currentDate().toString("yyyy-MM-dd")
            time_to_use = QTime.currentTime().toString("hh:mm")
        else:
            # استخدام الوقت المخصص
            current_date = self.current_date.toString("yyyy-MM-dd")
            time_to_use = self.current_time.toString("hh:mm")
        year,semester=self.get_current_school_year_and_semester()

        # طباعة ورقة التأخر
        try:
            # استخدام الطباعة الحرارية بتقنية تحويل النص إلى صورة
            if print_late_form_direct:
                print_success = print_late_form_direct(
                    students=selected_students,
                    section=section,
                    date_str=current_date,
                    time_str=time_to_use
                )
                if not print_success:
                    self.show_styled_message_box(
                        title="تنبيه",
                        message="فشلت الطباعة. تأكد من توصيل الطابعة الحرارية وتشغيلها.",
                        icon=QMessageBox.Warning,
                        button_color="#f44336",
                        button_hover_color="#d32f2f"
                    )
                    print_success = False
            else:
                # إذا كانت وحدة الطباعة غير متوفرة
                self.show_styled_message_box(
                    title="تنبيه",
                    message="وحدة الطباعة غير متوفرة",
                    icon=QMessageBox.Warning,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
                print_success = False

            if not print_success:
                self.show_styled_message_box(
                    title="تنبيه",
                    message="حدث خطأ أثناء طباعة ورقة التأخر",
                    icon=QMessageBox.Warning,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
        except Exception as e:
            print(f"خطأ في طباعة ورقة التأخر: {e}")
            self.show_styled_message_box(
                title="خطأ",
                message=f"حدث خطأ أثناء طباعة ورقة التأخر:\n{str(e)}",
                icon=QMessageBox.Critical,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )

        query_insert=QSqlQuery(db=self.db)
        query_update=QSqlQuery(db=self.db)
        success_count=0

        insert_prepared=query_insert.prepare("""
            INSERT INTO ورقة_السماح_بالدخول(
                السنة_الدراسية,
                الأسدس,
                الرقم_الترتيبي,
                التاريخ,
                الاسم_والنسب,
                سماح,
                الرمز,
                الوقت,
                ورقة_السماح
            ) VALUES (
                :year,
                :sem,
                (SELECT رت FROM اللوائح WHERE الرمز = :code AND السنة_الدراسية = :year),
                :date,
                :name,
                0,
                :code,
                :time,
                ' تأخر '
            )
        """)

        update_prepared = query_update.prepare("""
            UPDATE السجل_العام
            SET التأخر = (
                SELECT COUNT(*)
                FROM ورقة_السماح_بالدخول w
                WHERE w.الرمز = :code
                AND w.سماح = 0
                AND w.السنة_الدراسية = (SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1)
                AND w.الأسدس = (SELECT الأسدس FROM بيانات_المؤسسة LIMIT 1)
            )
            WHERE الرمز = :code
        """)

        if not insert_prepared: return
        if not update_prepared: return
        self.db.transaction()
        try:
            for student in selected_students:
                query_insert.bindValue(":year",year);
                query_insert.bindValue(":sem",semester);
                # لم نعد بحاجة إلى ربط الرقم الترتيبي هنا لأننا نستخدم استعلام فرعي
                # query_insert.bindValue(":rt",student['rt']);
                query_insert.bindValue(":date",current_date);
                query_insert.bindValue(":name",student['name']);
                query_insert.bindValue(":code",student['code']);
                query_insert.bindValue(":time",time_to_use);
                if query_insert.exec_(): query_update.bindValue(":code",student['code']);
                if not query_update.exec_(): pass; success_count+=1
                else: pass
            self.db.commit()

            # مسح تحديدات مربعات الاختيار بعد النجاح
            self.checkbox_delegate.checked_items.clear()
            self.table_lists.viewport().update()

        except Exception as e:
            self.db.rollback()
            self.show_styled_message_box(
                title="خطأ",
                message=f"حدث خطأ:\n{e}",
                icon=QMessageBox.Critical,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )
            return
        self.refresh_regulations_table()
        self.table_lists.clearSelection()

        # عرض رسالة نجاح مخصصة
        student_count = len(selected_students)
        if student_count > 0:
            if student_count == 1:
                message = f"تم تسجيل التأخر للتلميذ(ة) {selected_students[0]['name']} بنجاح"
            else:
                message = f"تم تسجيل التأخر لـ {student_count} تلاميذ بنجاح"

            self.show_custom_success_message(message, "تم التسجيل بنجاح")

    def print_student_card(self):
        selected_students=self.get_selected_students_info()
        if not selected_students:
            self.show_styled_message_box(
                title="تنبيه",
                message="الرجاء تحديد تلميذ واحد على الأقل",
                icon=QMessageBox.Information,
                button_color="#0066cc",
                button_hover_color="#0055bb"
            )
            return

        section=self.get_selected_section()
        if not section:
            self.show_styled_message_box(
                title="تنبيه",
                message="الرجاء تحديد قسم أولاً.",
                icon=QMessageBox.Warning,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )
            return

        # إنشاء نافذة تأكيد مخصصة
        confirm_dialog = QDialog(self)
        confirm_dialog.setWindowTitle("تأكيد طباعة ورقة توجيه التلميذ(ة)")
        confirm_dialog.setFixedSize(500, 400)
        confirm_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            confirm_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        confirm_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #0066cc;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QTextBrowser {
                border: 1px solid #3498db;
                border-radius: 5px;
                padding: 10px;
                background-color: white;
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
            }
            QPushButton#confirm_btn {
                background-color: #27ae60;
                color: white;
            }
            QPushButton#confirm_btn:hover {
                background-color: #2ecc71;
                border: 2px solid #27ae60;
            }
            QPushButton#cancel_btn {
                background-color: #e74c3c;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background-color: #c0392b;
                border: 2px solid #e74c3c;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(confirm_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة البرنامج
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            program_icon = QPixmap("01.ico")
            if not program_icon.isNull():
                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(program_icon)
                header_layout.addWidget(icon_label)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

        # إضافة عنوان النافذة
        title_label = QLabel("تأكيد طباعة ورقة توجيه التلميذ(ة)")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0066cc;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة التأكيد
        message_browser = QTextBrowser()
        message_browser.setReadOnly(True)

        # تنسيق النص بخط Calibri 13 أسود غامق وعرض المعلومات في جدول
        html_content = """
        <div dir='rtl' style='text-align: right;'>
            <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                سيتم طباعة ورقة توجيه التلميذ(ة) للتلاميذ التالية أسماؤهم:
            </p>

            <table style='width: 100%; border-collapse: collapse; margin-top: 10px; font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                <tr style='background-color: #0066cc; color: white;'>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>ر.ت</th>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>الرمز</th>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: right;'>الاسم والنسب</th>
                </tr>
        """

        # إضافة معلومات التلاميذ في جدول
        for student in selected_students:
            html_content += f"""
                <tr style='border-bottom: 1px solid #dddddd;'>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{student.get('rt', '')}</td>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{student['code']}</td>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: right;'>{student['name']}</td>
                </tr>
            """

        html_content += """
            </table>
        </div>
        """

        message_browser.setHtml(html_content)
        layout.addWidget(message_browser)

        # إضافة أزرار التأكيد والإلغاء
        buttons_layout = QHBoxLayout()

        confirm_btn = QPushButton("تأكيد")
        confirm_btn.setObjectName("confirm_btn")
        confirm_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        confirm_btn.setCursor(Qt.PointingHandCursor)
        confirm_btn.clicked.connect(confirm_dialog.accept)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_btn.setCursor(Qt.PointingHandCursor)
        cancel_btn.clicked.connect(confirm_dialog.reject)

        buttons_layout.addWidget(confirm_btn)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        # عرض النافذة وانتظار النتيجة
        reply = confirm_dialog.exec_() == QDialog.Accepted
        if not reply:
            return

        # تحديد التاريخ والوقت حسب الإعدادات
        if not self.use_custom_datetime:
            # استخدام الوقت الحالي
            current_date = QDate.currentDate().toString("yyyy-MM-dd")
            time_to_use = QTime.currentTime().toString("hh:mm")
        else:
            # استخدام الوقت المخصص
            current_date = self.current_date.toString("yyyy-MM-dd")
            time_to_use = self.current_time.toString("hh:mm")

        # محاولة استخدام الطباعة المباشرة أولاً، ثم الرجوع إلى الطريقة القديمة إذا فشلت
        try:
            # محاولة استخدام وحدة الطباعة المباشرة
            try:
                import thermal_image_print
                print_success = thermal_image_print.print_guidance_form_direct(
                    students=selected_students,
                    section=section,
                    date_str=current_date,
                    time_str=time_to_use
                )
                if not print_success:
                    # عرض رسالة خطأ
                    self.show_styled_message_box(
                        title="تنبيه",
                        message="حدث خطأ أثناء طباعة ورقة توجيه التلميذ(ة)",
                        icon=QMessageBox.Warning,
                        button_color="#f44336",
                        button_hover_color="#d32f2f"
                    )
            except ImportError:
                # عرض رسالة خطأ
                self.show_styled_message_box(
                    title="تنبيه",
                    message="وحدة الطباعة غير متوفرة",
                    icon=QMessageBox.Warning,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
            except Exception:
                # عرض رسالة خطأ
                self.show_styled_message_box(
                    title="تنبيه",
                    message="حدث خطأ في وحدة الطباعة",
                    icon=QMessageBox.Warning,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
            if not print_success:
                self.show_styled_message_box(
                    title="تنبيه",
                    message="حدث خطأ أثناء طباعة ورقة توجيه التلميذ(ة)",
                    icon=QMessageBox.Warning,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
            else:
                # مسح تحديدات مربعات الاختيار بعد النجاح
                self.checkbox_delegate.checked_items.clear()
                self.table_lists.viewport().update()
        except Exception as e:
            print(f"خطأ في طباعة ورقة توجيه التلميذ(ة): {e}")
            self.show_styled_message_box(
                title="خطأ",
                message=f"حدث خطأ أثناء طباعة ورقة توجيه التلميذ(ة):\n{str(e)}",
                icon=QMessageBox.Critical,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )

        self.table_lists.clearSelection()

    def show_violations_permission_slip(self):
        selected_students=self.get_selected_students_info()
        if not selected_students:
            self.show_styled_message_box(
                title="تنبيه",
                message="الرجاء تحديد تلميذ واحد على الأقل",
                icon=QMessageBox.Information,
                button_color="#0066cc",
                button_hover_color="#0055bb"
            )
            return

        section=self.get_selected_section()
        if not section:
            self.show_styled_message_box(
                title="تنبيه",
                message="الرجاء تحديد قسم أولاً.",
                icon=QMessageBox.Warning,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )
            return

        # إنشاء نافذة تأكيد مخصصة
        confirm_dialog = QDialog(self)
        confirm_dialog.setWindowTitle("تأكيد طباعة ورقة استئذان التلميذ(ة)")
        confirm_dialog.setFixedSize(500, 400)
        confirm_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            confirm_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        confirm_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #0066cc;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QTextBrowser {
                border: 1px solid #3498db;
                border-radius: 5px;
                padding: 10px;
                background-color: white;
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
            }
            QPushButton#confirm_btn {
                background-color: #27ae60;
                color: white;
            }
            QPushButton#confirm_btn:hover {
                background-color: #2ecc71;
                border: 2px solid #27ae60;
            }
            QPushButton#cancel_btn {
                background-color: #e74c3c;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background-color: #c0392b;
                border: 2px solid #e74c3c;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(confirm_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة البرنامج
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            program_icon = QPixmap("01.ico")
            if not program_icon.isNull():
                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(program_icon)
                header_layout.addWidget(icon_label)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

        # إضافة عنوان النافذة
        title_label = QLabel("تأكيد طباعة ورقة استئذان التلميذ(ة)")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0066cc;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة التأكيد
        message_browser = QTextBrowser()
        message_browser.setReadOnly(True)

        # تنسيق النص بخط Calibri 13 أسود غامق وعرض المعلومات في جدول
        html_content = """
        <div dir='rtl' style='text-align: right;'>
            <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                سيتم طباعة ورقة استئذان التلميذ(ة) للتلاميذ التالية أسماؤهم:
            </p>

            <table style='width: 100%; border-collapse: collapse; margin-top: 10px; font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                <tr style='background-color: #0066cc; color: white;'>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>ر.ت</th>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: right;'>الاسم والنسب</th>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>الرمز</th>
                </tr>
        """

        # إضافة معلومات التلاميذ في جدول
        for student in selected_students:
            html_content += f"""
                <tr style='border-bottom: 1px solid #dddddd;'>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{student.get('rt', '')}</td>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: right;'>{student['name']}</td>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{student['code']}</td>
                </tr>
            """

        html_content += """
            </table>
        </div>
        """

        message_browser.setHtml(html_content)
        layout.addWidget(message_browser)

        # إضافة أزرار التأكيد والإلغاء
        buttons_layout = QHBoxLayout()

        confirm_btn = QPushButton("تأكيد")
        confirm_btn.setObjectName("confirm_btn")
        confirm_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        confirm_btn.setCursor(Qt.PointingHandCursor)
        confirm_btn.setFixedWidth(105)  # تعديل العرض إلى 105
        confirm_btn.clicked.connect(confirm_dialog.accept)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_btn.setCursor(Qt.PointingHandCursor)
        cancel_btn.setFixedWidth(105)  # تعديل العرض إلى 105
        cancel_btn.clicked.connect(confirm_dialog.reject)

        buttons_layout.addWidget(confirm_btn)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        # عرض النافذة وانتظار النتيجة
        reply = confirm_dialog.exec_() == QDialog.Accepted
        if not reply:
            return

        # تحديد التاريخ والوقت حسب الإعدادات
        if not self.use_custom_datetime:
            # استخدام الوقت الحالي
            current_date = QDate.currentDate().toString("yyyy-MM-dd")
            time_to_use = QTime.currentTime().toString("hh:mm")
        else:
            # استخدام الوقت المخصص
            current_date = self.current_date.toString("yyyy-MM-dd")
            time_to_use = self.current_time.toString("hh:mm")

        try:
            # محاولة استخدام وحدة الطباعة المباشرة
            try:
                import thermal_image_print
                print_success = thermal_image_print.print_permission_form_direct(
                    students=selected_students,
                    section=section,
                    date_str=current_date,
                    time_str=time_to_use
                )
                if not print_success:
                    # عرض رسالة خطأ
                    self.show_styled_message_box(
                        title="تنبيه",
                        message="حدث خطأ أثناء طباعة ورقة استئذان التلميذ(ة)",
                        icon=QMessageBox.Warning,
                        button_color="#f44336",
                        button_hover_color="#d32f2f"
                    )
            except ImportError:
                # عرض رسالة خطأ
                self.show_styled_message_box(
                    title="تنبيه",
                    message="وحدة الطباعة غير متوفرة",
                    icon=QMessageBox.Warning,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
            except Exception:
                # عرض رسالة خطأ
                self.show_styled_message_box(
                    title="تنبيه",
                    message="حدث خطأ في وحدة الطباعة",
                    icon=QMessageBox.Warning,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
            if not print_success:
                self.show_enhanced_message("warning", "تنبيه", "حدث خطأ أثناء طباعة ورقة استئذان التلميذ(ة)")
            else:
                # مسح تحديدات مربعات الاختيار بعد النجاح
                self.checkbox_delegate.checked_items.clear()
                self.table_lists.viewport().update()
        except Exception as e:
            print(f"خطأ في طباعة ورقة استئذان التلميذ(ة): {e}")
            self.show_enhanced_message("error", "خطأ", f"حدث خطأ أثناء طباعة ورقة استئذان التلميذ(ة):\n{str(e)}")

        self.table_lists.clearSelection()

    def print_secret_code(self):
        selected_students=self.get_selected_students_info()
        if not selected_students:
            self.show_styled_message_box(
                title="تنبيه",
                message="الرجاء تحديد تلميذ واحد على الأقل",
                icon=QMessageBox.Information,
                button_color="#0066cc",
                button_hover_color="#0055bb"
            )
            return

        # إنشاء نافذة تأكيد مخصصة
        confirm_dialog = QDialog(self)
        confirm_dialog.setWindowTitle("تأكيد طباعة الرموز السرية للتلميذ(ة)")
        confirm_dialog.setFixedSize(500, 400)
        confirm_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            confirm_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        confirm_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #0066cc;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QTextBrowser {
                border: 1px solid #3498db;
                border-radius: 5px;
                padding: 10px;
                background-color: white;
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
            }
            QPushButton#confirm_btn {
                background-color: #27ae60;
                color: white;
            }
            QPushButton#confirm_btn:hover {
                background-color: #2ecc71;
                border: 2px solid #27ae60;
            }
            QPushButton#cancel_btn {
                background-color: #e74c3c;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background-color: #c0392b;
                border: 2px solid #e74c3c;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(confirm_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة البرنامج
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            program_icon = QPixmap("01.ico")
            if not program_icon.isNull():
                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(program_icon)
                header_layout.addWidget(icon_label)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

        # إضافة عنوان النافذة
        title_label = QLabel("تأكيد طباعة الرموز السرية للتلميذ(ة)")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0066cc;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة التأكيد
        message_browser = QTextBrowser()
        message_browser.setReadOnly(True)

        # تنسيق النص بخط Calibri 13 أسود غامق وعرض المعلومات في جدول
        html_content = """
        <div dir='rtl' style='text-align: right;'>
            <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                سيتم طباعة الرموز السرية للتلاميذ التالية أسماؤهم:
            </p>

            <table style='width: 100%; border-collapse: collapse; margin-top: 10px; font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                <tr style='background-color: #0066cc; color: white;'>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>ر.ت</th>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: right;'>الاسم والنسب</th>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>الرمز</th>
                </tr>
        """

        # إضافة معلومات التلاميذ في جدول
        for student in selected_students:
            html_content += f"""
                <tr style='border-bottom: 1px solid #dddddd;'>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{student.get('rt', '')}</td>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: right;'>{student['name']}</td>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{student['code']}</td>
                </tr>
            """

        html_content += """
            </table>
        </div>
        """

        message_browser.setHtml(html_content)
        layout.addWidget(message_browser)

        # إضافة أزرار التأكيد والإلغاء
        buttons_layout = QHBoxLayout()

        confirm_btn = QPushButton("تأكيد")
        confirm_btn.setObjectName("confirm_btn")
        confirm_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        confirm_btn.setCursor(Qt.PointingHandCursor)
        confirm_btn.setFixedWidth(105)  # تعديل العرض إلى 105
        confirm_btn.clicked.connect(confirm_dialog.accept)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_btn.setCursor(Qt.PointingHandCursor)
        cancel_btn.setFixedWidth(105)  # تعديل العرض إلى 105
        cancel_btn.clicked.connect(confirm_dialog.reject)

        buttons_layout.addWidget(confirm_btn)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        # عرض النافذة وانتظار النتيجة
        reply = confirm_dialog.exec_() == QDialog.Accepted
        if not reply:
            return

        # جمع الرموز السرية للتلاميذ المحددين
        students_with_secrets = []
        for student in selected_students:
            query = QSqlQuery(db=self.db)
            query.prepare("SELECT الرمز_السري FROM السجل_العام WHERE الرمز = ?")
            query.bindValue(0, student['code'])
            if query.exec_() and query.next():
                secret_code = query.value(0) or "غير متوفر"
                student['secret'] = secret_code
                students_with_secrets.append(student)

        # تحديد التاريخ حسب الإعدادات
        if not self.use_custom_datetime:
            # استخدام التاريخ الحالي
            current_date = QDate.currentDate().toString("yyyy-MM-dd")
        else:
            # استخدام التاريخ المخصص
            current_date = self.current_date.toString("yyyy-MM-dd")

        try:
            # محاولة استخدام وحدة الطباعة المباشرة
            try:
                import thermal_image_print
                print_success = thermal_image_print.print_secret_codes_direct(
                    students=students_with_secrets,
                    date_str=current_date
                )
                if not print_success:
                    # عرض رسالة خطأ
                    self.show_styled_message_box(
                        title="تنبيه",
                        message="حدث خطأ أثناء طباعة الرموز السرية للتلميذ(ة)",
                        icon=QMessageBox.Warning,
                        button_color="#f44336",
                        button_hover_color="#d32f2f"
                    )
            except ImportError:
                # عرض رسالة خطأ
                self.show_styled_message_box(
                    title="تنبيه",
                    message="وحدة الطباعة غير متوفرة",
                    icon=QMessageBox.Warning,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
                print_success = False
            except Exception:
                # عرض رسالة خطأ
                self.show_styled_message_box(
                    title="تنبيه",
                    message="حدث خطأ في وحدة الطباعة",
                    icon=QMessageBox.Warning,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
                print_success = False
            if not print_success:
                self.show_styled_message_box(
                    title="تنبيه",
                    message="حدث خطأ أثناء طباعة الرموز السرية للتلميذ(ة)",
                    icon=QMessageBox.Warning,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
            else:
                # مسح تحديدات مربعات الاختيار بعد النجاح
                self.checkbox_delegate.checked_items.clear()
                self.table_lists.viewport().update()
        except Exception as e:
            print(f"خطأ في طباعة الرموز السرية للتلميذ(ة): {e}")
            self.show_styled_message_box(
                title="خطأ",
                message=f"حدث خطأ أثناء طباعة الرموز السرية للتلميذ(ة):\n{str(e)}",
                icon=QMessageBox.Critical,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )

        self.table_lists.clearSelection()

    def open_absence_justification(self):
        """إضافة طلبات الشهادة المدرسية"""
        selected_students = self.get_selected_students_info()
        if not selected_students:
            self.show_styled_message_box(
                title="تنبيه",
                message="الرجاء تحديد تلميذ واحد على الأقل",
                icon=QMessageBox.Information,
                button_color="#0066cc",
                button_hover_color="#0055bb"
            )
            return

        section = self.get_selected_section()
        if not section:
            self.show_styled_message_box(
                title="تنبيه",
                message="الرجاء تحديد قسم أولاً.",
                icon=QMessageBox.Warning,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )
            return

        # إنشاء نافذة تأكيد مخصصة
        confirm_dialog = QDialog(self)
        confirm_dialog.setWindowTitle("تأكيد إضافة طلبات الشهادة المدرسية")
        confirm_dialog.setFixedSize(500, 400)
        confirm_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            confirm_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تنسيق النافذة
        confirm_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #0066cc;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-weight: bold;
            }
            QTextBrowser {
                border: 1px solid #3498db;
                border-radius: 5px;
                padding: 10px;
                background-color: white;
            }
            QPushButton {
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                min-height: 35px;
            }
            QPushButton#confirm_btn {
                background-color: #27ae60;
                color: white;
            }
            QPushButton#confirm_btn:hover {
                background-color: #2ecc71;
                border: 2px solid #27ae60;
            }
            QPushButton#cancel_btn {
                background-color: #e74c3c;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background-color: #c0392b;
                border: 2px solid #e74c3c;
            }
        """)

        # إنشاء تخطيط النافذة
        layout = QVBoxLayout(confirm_dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة البرنامج
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            program_icon = QPixmap("01.ico")
            if not program_icon.isNull():
                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(program_icon)
                header_layout.addWidget(icon_label)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

        # إضافة عنوان النافذة
        title_label = QLabel("تأكيد إضافة طلبات الشهادة المدرسية")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0066cc;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        layout.addLayout(header_layout)

        # إضافة رسالة التأكيد
        message_browser = QTextBrowser()
        message_browser.setReadOnly(True)

        # تنسيق النص بخط Calibri 13 أسود غامق وعرض المعلومات في جدول
        html_content = """
        <div dir='rtl' style='text-align: right;'>
            <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                سيتم إضافة طلبات الشهادة المدرسية للتلاميذ التالية أسماؤهم:
            </p>

            <table style='width: 100%; border-collapse: collapse; margin-top: 10px; font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                <tr style='background-color: #0066cc; color: white;'>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>ر.ت</th>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: right;'>الاسم والنسب</th>
                    <th style='border: 1.5px solid #0055bb; padding: 8px; text-align: center;'>الرمز</th>
                </tr>
        """

        # إضافة معلومات التلاميذ في جدول
        for student in selected_students:
            html_content += f"""
                <tr style='border-bottom: 1px solid #dddddd;'>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{student.get('rt', '')}</td>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: right;'>{student['name']}</td>
                    <td style='border: 1.5px solid #0066cc; padding: 8px; text-align: center;'>{student['code']}</td>
                </tr>
            """

        html_content += """
            </table>
        </div>
        """

        message_browser.setHtml(html_content)
        layout.addWidget(message_browser)

        # إضافة أزرار التأكيد والإلغاء
        buttons_layout = QHBoxLayout()

        confirm_btn = QPushButton("تأكيد")
        confirm_btn.setObjectName("confirm_btn")
        confirm_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        confirm_btn.setCursor(Qt.PointingHandCursor)
        confirm_btn.clicked.connect(confirm_dialog.accept)

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setObjectName("cancel_btn")
        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
        cancel_btn.setCursor(Qt.PointingHandCursor)
        cancel_btn.clicked.connect(confirm_dialog.reject)

        buttons_layout.addWidget(confirm_btn)
        buttons_layout.addWidget(cancel_btn)

        layout.addLayout(buttons_layout)

        # عرض النافذة وانتظار النتيجة
        reply = confirm_dialog.exec_() == QDialog.Accepted
        if not reply:
            return

        try:
            # الحصول على البيانات من جدول بيانات_المؤسسة
            query = QSqlQuery(db=self.db)
            query.exec_("""
                SELECT السنة_الدراسية, رقم_الحراسة
                FROM بيانات_المؤسسة
                LIMIT 1
            """)

            if query.next():
                school_year = query.value(0)
                guard_office = query.value(1)
            else:
                self.show_enhanced_message("warning", "تنبيه", "لم يتم العثور على بيانات المؤسسة")
                return

            # إعداد استعلام الإدراج بالبارامترات المطلوبة
            insert_query = QSqlQuery(db=self.db)
            insert_query.prepare("""
                INSERT INTO الشهادة_المدرسية (
                    الرمز,
                    السنة,
                    الرقم,
                    القسم,
                    الاسم_والنسب,
                    تاريخ_الطلب,
                    مكتب_الحراسة,
                    تاريخ_التسليم,
                    ملاحظات,
                    اختيار
                ) VALUES (
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    ?,
                    NULL,
                    NULL,
                    0
                )
            """)

            # بدء المعاملة
            self.db.transaction()
            current_date = QDate.currentDate().toString("yyyy-MM-dd")
            success_count = 0

            # الحصول على آخر رقم مستخدم
            last_number_query = QSqlQuery(db=self.db)
            last_number_query.exec_("SELECT COALESCE(MAX(الرقم), 0) FROM الشهادة_المدرسية")
            last_number = 0
            if last_number_query.next():
                last_number = last_number_query.value(0)

            try:
                for i, student in enumerate(selected_students, 1):
                    # ربط القيم بالترتيب الصحيح
                    insert_query.addBindValue(student['code'])         # رقم_التسجيل
                    insert_query.addBindValue(school_year)            # السنة
                    insert_query.addBindValue(last_number + i)        # الرقم
                    insert_query.addBindValue(section)                # القسم
                    insert_query.addBindValue(student['name'])        # الاسم_والنسب
                    insert_query.addBindValue(current_date)           # تاريخ_الطلب
                    insert_query.addBindValue(guard_office)           # مكتب_الحراسة

                    if insert_query.exec_():
                        success_count += 1
                    else:
                        print(f"خطأ في إضافة الطالب {student['name']}: {insert_query.lastError().text()}")

                if success_count > 0:
                    self.db.commit()
                    self.show_styled_message_box(
                        title="نجاح",
                        message=f"تم إضافة {success_count} طلب/طلبات بنجاح",
                        icon=QMessageBox.Information,
                        button_color="#4CAF50",
                        button_hover_color="#45a049"
                    )
                    # مسح تحديدات مربعات الاختيار بعد النجاح
                    self.checkbox_delegate.checked_items.clear()
                    self.table_lists.viewport().update()
                else:
                    self.db.rollback()
                    self.show_styled_message_box(
                        title="تنبيه",
                        message="لم يتم إضافة أي طلبات",
                        icon=QMessageBox.Warning,
                        button_color="#f44336",
                        button_hover_color="#d32f2f"
                    )

            except Exception as e:
                self.db.rollback()
                self.show_styled_message_box(
                    title="خطأ",
                    message=f"حدث خطأ أثناء إضافة الطلبات:\n{str(e)}",
                    icon=QMessageBox.Critical,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )

        except Exception as e:
            self.show_styled_message_box(
                title="خطأ",
                message=f"حدث خطأ غير متوقع:\n{str(e)}",
                icon=QMessageBox.Critical,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )

    def open_violations_window(self):
        """فتح نافذة مسك المخالفات للتلميذ المحدد"""
        selected_students = self.get_selected_students_info()
        
        if not selected_students:
            self.show_styled_message_box(
                title="تنبيه",
                message="يرجى تحديد تلميذ واحد على الأقل لمسك المخالفات",
                icon=QMessageBox.Information,
                button_color="#0066cc",
                button_hover_color="#0055bb"
            )
            return

        # أخذ التلميذ الأول المحدد
        student = selected_students[0]
        student_name = student.get('name', 'غير محدد')
        student_code = student.get('code', 'غير محدد')
        
        try:
            # استيراد نافذة مسك المخالفات الحديثة
            from sub12_window_html import StudentViolationsWindow
            
            # تحديد مسار قاعدة البيانات الصحيح
            import os
            script_dir = os.path.dirname(os.path.abspath(__file__))
            db_path = os.path.join(script_dir, "data.db")
            
            print(f"فتح نافذة المخالفات للتلميذ: {student_name} (رمز: {student_code})")
            print(f"مسار قاعدة البيانات: {db_path}")
            
            # إنشاء وعرض نافذة مسك المخالفات
            violations_window = StudentViolationsWindow(
                student_code=student_code,
                student_name=student_name,
                db_path=db_path,  # استخدام مسار الملف بدلاً من اتصال قاعدة البيانات
                parent=self
            )
            violations_window.show()
            
            # التأكد من فتح النافذة في كامل الشاشة
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(100, violations_window.open_fullscreen)
            
        except ImportError as e:
            print(f"خطأ في استيراد نافذة مسك المخالفات: {e}")
            self.show_styled_message_box(
                title="خطأ",
                message="تعذر فتح نافذة مسك المخالفات. يرجى التأكد من وجود الملف المطلوب.",
                icon=QMessageBox.Critical,
                button_color="#e74c3c",
                button_hover_color="#c0392b"
            )
        except Exception as e:
            print(f"خطأ عام في فتح نافذة مسك المخالفات: {e}")
            self.show_styled_message_box(
                title="خطأ",
                message=f"حدث خطأ غير متوقع: {str(e)}",
                icon=QMessageBox.Critical,
                button_color="#e74c3c",
                button_hover_color="#c0392b"
            )

    def show_help(self):
        """عرض نافذة التعليمات للمستخدم بتصميم مميز وأنيق"""
        help_dialog = QDialog(self)
        help_dialog.setWindowTitle("تعليمات استخدام نافذة اللوائح والأقسام")
        help_dialog.setMinimumSize(900, 700)
        help_dialog.setLayoutDirection(Qt.RightToLeft)

        # إضافة أيقونة البرنامج
        try:
            app_icon = QIcon("01.ico")
            help_dialog.setWindowIcon(app_icon)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

        # تطبيق التصميم على النافذة
        help_dialog.setStyleSheet("""
            QDialog {
                background-color: #f0f8ff;
                border: 2px solid #0066cc;
                border-radius: 10px;
            }
            QTextBrowser {
                border: 1px solid #3498db;
                border-radius: 8px;
                padding: 15px;
                font-family: Calibri;
                font-size: 16pt;
                line-height: 1.6;
                background-color: white;
                text-align: right;
            }
            QScrollBar:vertical {
                border: none;
                background: #f0f0f0;
                width: 12px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #0066cc;
                min-height: 20px;
                border-radius: 6px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """)

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(help_dialog)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # إنشاء إطار للعنوان
        title_frame = QFrame()
        title_frame.setFrameShape(QFrame.StyledPanel)
        title_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #0066cc;
                border-radius: 10px;
                background-color: #e6f2ff;
                padding: 10px;
                margin-bottom: 10px;
            }
        """)
        title_layout = QVBoxLayout(title_frame)

        # إضافة أيقونة وعنوان
        header_layout = QHBoxLayout()

        # محاولة إضافة أيقونة البرنامج
        icon_label = QLabel()
        icon_label.setAlignment(Qt.AlignCenter)
        try:
            program_icon = QPixmap("01.ico")
            if not program_icon.isNull():
                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon_label.setPixmap(program_icon)
                header_layout.addWidget(icon_label)
        except Exception as e:
            print(f"خطأ في تحميل أيقونة البرنامج للعنوان: {e}")

        # إضافة عنوان النافذة
        title_label = QLabel("دليل استخدام نافذة اللوائح والأقسام")
        title_label.setFont(QFont("Calibri", 20, QFont.Bold))
        title_label.setStyleSheet("color: #0066cc; font-weight: bold;")
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label, 1)

        title_layout.addLayout(header_layout)
        main_layout.addWidget(title_frame)

        # إنشاء متصفح النص
        text_browser = QTextBrowser()
        text_browser.setOpenExternalLinks(True)

        # محتوى التعليمات
        help_text = """
        <h3 style="color: #0066cc; border-bottom: 2px solid #0066cc; padding-bottom: 5px;">نظرة عامة</h3>
        <p>هذه النافذة تساعدك على إدارة لوائح التلاميذ والأقسام وتنفيذ مختلف العمليات المتعلقة بهم مثل تسجيل الدخول والتأخر وطباعة الأوراق المختلفة.</p>

        <h3 style="color: #0066cc; border-bottom: 2px solid #0066cc; padding-bottom: 5px;">مكونات النافذة</h3>
        <ul>
            <li><b>الشريط العلوي:</b> يحتوي على أدوات اختيار الحراسة والتاريخ والوقت.</li>
            <li><b>شريط الأزرار:</b> يحتوي على الأزرار الرئيسية للعمليات المختلفة.</li>
            <li><b>جدول المستويات:</b> يعرض المستويات الدراسية المتوفرة.</li>
            <li><b>جدول الأقسام:</b> يعرض أقسام المستوى المحدد.</li>
            <li><b>جدول التلاميذ:</b> يعرض تلاميذ القسم المحدد مع إمكانية تحديدهم.</li>
        </ul>

        <h3 style="color: #0066cc; border-bottom: 2px solid #0066cc; padding-bottom: 5px;">خطوات الاستخدام الأساسية</h3>
        <ol>
            <li><b>اختيار الحراسة:</b> استخدم القائمة المنسدلة "حراسة رقم" لاختيار الحراسة المطلوبة.</li>
            <li><b>تحديد المستوى:</b> انقر على المستوى المطلوب في الجدول الأيمن.</li>
            <li><b>تحديد القسم:</b> انقر على القسم المطلوب في الجدول الأسفل.</li>
            <li><b>تحديد التلاميذ:</b> حدد التلميذ أو التلاميذ بالنقر على مربع الاختيار في العمود الأول من جدول التلاميذ.</li>
            <li><b>تنفيذ العملية:</b> اضغط على الزر المناسب لتنفيذ العملية المطلوبة على التلاميذ المحددين.</li>
        </ol>

        <h3 style="color: #0066cc; border-bottom: 2px solid #0066cc; padding-bottom: 5px;">وظائف الأزرار</h3>
        <ul>
            <li><b style="color: #0066cc;">ورقة الدخول:</b> تسجيل وطباعة ورقة السماح بالدخول للتلاميذ المحددين.</li>
            <li><b style="color: #0066cc;">ورقة التأخر:</b> تسجيل وطباعة ورقة التأخر للتلاميذ المحددين.</li>
            <li><b style="color: #0066cc;">ورقة توجيه التلميذ(ة):</b> طباعة ورقة التوجيه للتلاميذ المحددين.</li>
            <li><b style="color: #0066cc;">ورقة استئذان التلميذ(ة):</b> طباعة ورقة الاستئذان للتلاميذ المحددين.</li>
            <li><b style="color: #0066cc;">الرمز السري للتلميذ(ة):</b> طباعة الرموز السرية للتلاميذ المحددين.</li>
            <li><b style="color: #0066cc;">مسك الطلبات:</b> إضافة طلبات الشهادة المدرسية للتلاميذ المحددين.</li>
            <li><b style="color: #0066cc;">زيارة الطبيب:</b> طباعة نموذج زيارة الطبيب للتلاميذ المحددين.</li>
        </ul>

        <h3 style="color: #0066cc; border-bottom: 2px solid #0066cc; padding-bottom: 5px;">ميزات إضافية</h3>
        <ul>
            <li><b>عرض بطاقة التلميذ:</b> يمكنك النقر على رمز التلميذ (العمود الثالث) لفتح بطاقة التلميذ الكاملة.</li>
            <li><b>تحديد متعدد:</b> يمكنك تحديد عدة تلاميذ في نفس الوقت باستخدام مربعات الاختيار.</li>
            <li><b>التاريخ والوقت:</b> يمكنك تحديد التاريخ والوقت يدوياً أو استخدام الوقت الحالي تلقائياً.</li>
        </ul>

        <h3 style="color: #0066cc; border-bottom: 2px solid #0066cc; padding-bottom: 5px;">ملاحظات هامة</h3>
        <ul>
            <li>تأكد من تحديد التاريخ والوقت المناسبين قبل تنفيذ أي عملية.</li>
            <li>عند تفعيل خيار "الوقت الحالي"، سيتم استخدام التاريخ والوقت الحاليين تلقائياً.</li>
            <li>بعد تنفيذ أي عملية، سيتم تحديث البيانات في الجدول تلقائياً.</li>
            <li>يمكنك استخدام مربعات الاختيار لتحديد عدة تلاميذ وتنفيذ العملية عليهم دفعة واحدة.</li>
        </ul>
        """

        text_browser.setHtml(help_text)
        main_layout.addWidget(text_browser)

        # إضافة أزرار في الأسفل
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        close_btn.setCursor(Qt.PointingHandCursor)
        close_btn.setFixedHeight(40)
        close_btn.setFixedWidth(105)  # تعديل العرض إلى 105
        close_btn.clicked.connect(help_dialog.accept)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #0066cc;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 20px;
            }
            QPushButton:hover {
                background-color: #0055bb;
                border: 2px solid #003c8f;
            }
            QPushButton:pressed {
                background-color: #003c8f;
            }
        """)

        buttons_layout.addWidget(close_btn)
        main_layout.addLayout(buttons_layout)

        # عرض النافذة
        help_dialog.exec_()

    def print_doctor_visit(self):
        """طباعة نموذج زيارة الطبيب"""
        selected_students = self.get_selected_students_info()
        if not selected_students:
            self.show_styled_message_box(
                title="تنبيه",
                message="الرجاء تحديد تلميذ واحد على الأقل",
                icon=QMessageBox.Information,
                button_color="#0066cc",
                button_hover_color="#0055bb"
            )
            return

        section = self.get_selected_section()
        if not section:
            self.show_styled_message_box(
                title="تنبيه",
                message="الرجاء تحديد قسم أولاً.",
                icon=QMessageBox.Warning,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )
            return

        # تحديد التاريخ والوقت حسب الإعدادات
        if not self.use_custom_datetime:
            # استخدام الوقت الحالي
            current_date = QDate.currentDate().toString("yyyy-MM-dd")
            time_to_use = QTime.currentTime().toString("hh:mm")
        else:
            # استخدام الوقت المخصص
            current_date = self.current_date.toString("yyyy-MM-dd")
            time_to_use = self.current_time.toString("hh:mm")

        try:
            # استخدام دالة print_doctor_visit_form من ملف print1.py
            try:
                # محاولة استيراد الدالة مباشرة
                import print1
                print_doctor_visit_form_new = print1.print_doctor_visit_form
                new_doctor_visit_form_imported = True
            except ImportError:
                new_doctor_visit_form_imported = False

            if new_doctor_visit_form_imported:
                # استخدام الدالة الجديدة من ملف print1.py
                print_result, filepath, reports_dir = print_doctor_visit_form_new(
                    students=selected_students,
                    section=section,
                    date_str=current_date,
                    time_str=time_to_use,
                    use_regular_printer=True  # استخدام الطابعة العادية
                )

                if print_result:

                    # استخراج معلومات التلميذ لعرضها في رسالة التأكيد
                    student_name = selected_students[0].get('name', '')
                    student_code = selected_students[0].get('code', '')
                    current_date_display = datetime.datetime.now().strftime('%Y-%m-%d')

                    # استخدام نموذج الرسائل المميزة من ملف sub100_window.py إذا كان متوفراً
                    if CUSTOM_DIALOGS_IMPORTED:
                        # إنشاء رسالة نجاح مخصصة باستخدام فئة ConfirmationDialogs
                        success_message = f"تمت طباعة ورقة زيارة الطبيب بنجاح\n\n"
                        success_message += f"اسم التلميذ: {student_name}\n"
                        success_message += f"رمز التلميذ: {student_code}\n"
                        success_message += f"تاريخ الطلب: {current_date_display}\n\n"
                        success_message += f"هل تريد معاينة التقرير أو فتح المجلد؟"

                        # إنشاء نافذة حوار مخصصة
                        dialog = QDialog(self)
                        dialog.setWindowTitle("تمت طباعة ورقة زيارة الطبيب بنجاح")
                        dialog.setFixedSize(500, 400)
                        dialog.setLayoutDirection(Qt.RightToLeft)
                        dialog.setWindowIcon(QIcon("01.ico"))

                        # تنسيق النافذة
                        dialog.setStyleSheet("""
                            QDialog {
                                background-color: #f0f8ff;
                                border: 2px solid #0066cc;
                                border-radius: 10px;
                            }
                            QLabel {
                                color: #333333;
                                font-weight: bold;
                            }
                            QTextBrowser {
                                border: 1px solid #3498db;
                                border-radius: 5px;
                                padding: 10px;
                                background-color: white;
                            }
                            QPushButton {
                                border-radius: 5px;
                                padding: 8px 15px;
                                font-weight: bold;
                                min-height: 35px;
                            }
                            QPushButton#preview_btn {
                                background-color: #27ae60;
                                color: white;
                            }
                            QPushButton#preview_btn:hover {
                                background-color: #2ecc71;
                                border: 2px solid #27ae60;
                            }
                            QPushButton#folder_btn {
                                background-color: #3498db;
                                color: white;
                            }
                            QPushButton#folder_btn:hover {
                                background-color: #2980b9;
                                border: 2px solid #3498db;
                            }
                            QPushButton#cancel_btn {
                                background-color: #e74c3c;
                                color: white;
                            }
                            QPushButton#cancel_btn:hover {
                                background-color: #c0392b;
                                border: 2px solid #e74c3c;
                            }
                        """)

                        # إنشاء تخطيط النافذة
                        layout = QVBoxLayout(dialog)
                        layout.setContentsMargins(20, 20, 20, 20)
                        layout.setSpacing(15)

                        # إضافة أيقونة وعنوان
                        header_layout = QHBoxLayout()

                        # إضافة أيقونة البرنامج
                        icon_label = QLabel()
                        icon_label.setAlignment(Qt.AlignCenter)
                        try:
                            program_icon = QPixmap("01.ico")
                            if not program_icon.isNull():
                                program_icon = program_icon.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                                icon_label.setPixmap(program_icon)
                                header_layout.addWidget(icon_label)
                        except Exception as e:
                            print(f"خطأ في تحميل أيقونة البرنامج: {e}")

                        # إضافة عنوان النافذة
                        title_label = QLabel("تمت طباعة ورقة زيارة الطبيب بنجاح")
                        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
                        title_label.setStyleSheet("color: #0066cc;")
                        title_label.setAlignment(Qt.AlignCenter)
                        header_layout.addWidget(title_label, 1)

                        layout.addLayout(header_layout)

                        # إضافة رسالة التأكيد
                        message_browser = QTextBrowser()
                        message_browser.setReadOnly(True)

                        # تنسيق النص بخط Calibri 13 أسود غامق
                        html_content = f"""
                        <div dir='rtl' style='text-align: right;'>
                            <p style='font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                                تمت طباعة ورقة زيارة الطبيب بنجاح
                            </p>

                            <table style='width: 100%; border-collapse: collapse; margin-top: 10px; font-family: Calibri; font-size: 13pt; color: black; font-weight: bold;'>
                                <tr>
                                    <td style='padding: 8px; text-align: right;'>اسم التلميذ:</td>
                                    <td style='padding: 8px; text-align: right;'>{student_name}</td>
                                </tr>
                                <tr>
                                    <td style='padding: 8px; text-align: right;'>رمز التلميذ:</td>
                                    <td style='padding: 8px; text-align: right;'>{student_code}</td>
                                </tr>
                                <tr>
                                    <td style='padding: 8px; text-align: right;'>تاريخ الطلب:</td>
                                    <td style='padding: 8px; text-align: right;'>{current_date_display}</td>
                                </tr>
                                <tr>
                                    <td style='padding: 8px; text-align: right;'>مسار الملف:</td>
                                    <td style='padding: 8px; text-align: right;'>{reports_dir}</td>
                                </tr>
                            </table>

                            <p style='font-family: Calibri; font-size: 13pt; color: #0066cc; font-weight: bold; margin-top: 20px;'>
                                هل تريد معاينة التقرير أو فتح المجلد؟
                            </p>
                        </div>
                        """

                        message_browser.setHtml(html_content)
                        layout.addWidget(message_browser)

                        # إضافة أزرار
                        buttons_layout = QHBoxLayout()

                        preview_btn = QPushButton("معاينة الملف")
                        preview_btn.setObjectName("preview_btn")
                        preview_btn.setFont(QFont("Calibri", 12, QFont.Bold))
                        preview_btn.setCursor(Qt.PointingHandCursor)
                        preview_btn.setFixedWidth(120)
                        preview_btn.clicked.connect(lambda: self.handle_doctor_visit_action(dialog, "preview", filepath))

                        folder_btn = QPushButton("فتح المجلد")
                        folder_btn.setObjectName("folder_btn")
                        folder_btn.setFont(QFont("Calibri", 12, QFont.Bold))
                        folder_btn.setCursor(Qt.PointingHandCursor)
                        folder_btn.setFixedWidth(120)
                        folder_btn.clicked.connect(lambda: self.handle_doctor_visit_action(dialog, "folder", reports_dir))

                        cancel_btn = QPushButton("إغلاق")
                        cancel_btn.setObjectName("cancel_btn")
                        cancel_btn.setFont(QFont("Calibri", 12, QFont.Bold))
                        cancel_btn.setCursor(Qt.PointingHandCursor)
                        cancel_btn.setFixedWidth(120)
                        cancel_btn.clicked.connect(dialog.reject)

                        buttons_layout.addWidget(preview_btn)
                        buttons_layout.addWidget(folder_btn)
                        buttons_layout.addWidget(cancel_btn)

                        layout.addLayout(buttons_layout)

                        # عرض النافذة
                        dialog.exec_()
                    else:
                        # عرض رسالة نجاح مع معلومات عن مكان حفظ الملف
                        self.show_styled_message_box(
                            title="نجاح",
                            message=f"تمت طباعة ورقة زيارة الطبيب بنجاح\n\n"
                                   f"اسم التلميذ: {student_name}\n"
                                   f"رمز التلميذ: {student_code}\n"
                                   f"تاريخ الطلب: {current_date_display}\n\n"
                                   f"تم حفظ الملف في المجلد:\n{reports_dir}",
                            icon=QMessageBox.Information,
                            button_color="#4CAF50",
                            button_hover_color="#45a049"
                        )

                    # مسح تحديدات مربعات الاختيار بعد النجاح
                    self.checkbox_delegate.checked_items.clear()
                    self.table_lists.viewport().update()
                else:
                    self.show_styled_message_box(
                        title="تنبيه",
                        message="حدث خطأ أثناء طباعة ورقة زيارة الطبيب",
                        icon=QMessageBox.Warning,
                        button_color="#f44336",
                        button_hover_color="#d32f2f"
                    )
            elif print_doctor_visit_form_old:
                # استخدام الدالة القديمة للتوافقية
                print_success = print_doctor_visit_form_old(
                    students=selected_students,
                    section=section,
                    date_str=current_date,
                    time_str=time_to_use,
                    use_regular_printer=True  # استخدام الطابعة العادية
                )

                if print_success:

                    # إنشاء مسار المجلد للعرض في رسالة التأكيد
                    import os
                    doctor_visit_dir = os.path.join(os.path.dirname(__file__), "تقارير_زيارة_الطبيب")

                    # استخراج معلومات التلميذ لعرضها في رسالة التأكيد
                    student_name = selected_students[0].get('name', '')
                    student_code = selected_students[0].get('code', '')
                    current_date_display = datetime.datetime.now().strftime('%Y-%m-%d')

                    # عرض رسالة نجاح مع معلومات عن مكان حفظ الملف
                    self.show_styled_message_box(
                        title="نجاح",
                        message=f"تمت طباعة ورقة زيارة الطبيب بنجاح\n\n"
                               f"اسم التلميذ: {student_name}\n"
                               f"رمز التلميذ: {student_code}\n"
                               f"تاريخ الطلب: {current_date_display}\n\n"
                               f"تم حفظ الملف في المجلد:\n{doctor_visit_dir}",
                        icon=QMessageBox.Information,
                        button_color="#4CAF50",
                        button_hover_color="#45a049"
                    )

                    # مسح تحديدات مربعات الاختيار بعد النجاح
                    self.checkbox_delegate.checked_items.clear()
                    self.table_lists.viewport().update()
                else:
                    self.show_styled_message_box(
                        title="تنبيه",
                        message="حدث خطأ أثناء طباعة ورقة زيارة الطبيب",
                        icon=QMessageBox.Warning,
                        button_color="#f44336",
                        button_hover_color="#d32f2f"
                    )
            else:
                self.show_styled_message_box(
                    title="تنبيه",
                    message="وحدة طباعة ورقة زيارة الطبيب غير متوفرة",
                    icon=QMessageBox.Warning,
                    button_color="#f44336",
                    button_hover_color="#d32f2f"
                )
        except Exception as e:
            print(f"خطأ في طباعة ورقة زيارة الطبيب: {e}")
            self.show_styled_message_box(
                title="خطأ",
                message=f"حدث خطأ أثناء طباعة ورقة زيارة الطبيب:\n{str(e)}",
                icon=QMessageBox.Critical,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )

        self.table_lists.clearSelection()

    def handle_doctor_visit_action(self, dialog, action_type, path):
        """معالجة إجراءات نافذة زيارة الطبيب المخصصة"""
        # إغلاق نافذة الحوار
        dialog.accept()

        # تنفيذ الإجراء المطلوب
        try:
            if action_type == "preview":
                # فتح الملف للمعاينة
                os.startfile(path)
            elif action_type == "folder":
                # فتح مجلد التقارير
                os.startfile(path)
        except Exception as e:
            # عرض رسالة خطأ في حالة فشل فتح الملف أو المجلد
            self.show_styled_message_box(
                title="خطأ",
                message=f"حدث خطأ أثناء محاولة فتح {path}:\n{str(e)}",
                icon=QMessageBox.Critical,
                button_color="#f44336",
                button_hover_color="#d32f2f"
            )



    def show_student_data(self):
        """عرض بيانات التلميذ المحدد"""
        # التحقق من وجود تلميذ محدد
        checked_rows = self.checkbox_delegate.get_checked_rows()
        print(f"التشخيص: عدد الصفوف المحددة: {len(checked_rows) if checked_rows else 0}")
        
        if not checked_rows:
            self.show_enhanced_message(
                "warning",
                "تنبيه",
                "يرجى تحديد تلميذ واحد على الأقل لعرض بياناته"
            )
            return

        # أخذ أول تلميذ محدد
        row = checked_rows[0]
        code_index = self.model_lists.index(row, 2)  # الرمز في العمود 2
        code = self.model_lists.data(code_index, Qt.DisplayRole)
        
        print(f"التشخيص: رمز التلميذ المحدد: {code}")
        print(f"التشخيص: السنة الدراسية الحالية: {self.current_academic_year}")
        print(f"التشخيص: هل StudentCardWindow متاح؟ {StudentCardWindow is not None}")

        if code and StudentCardWindow:
            try:
                # إنشاء نافذة بطاقة التلميذ مع المعاملات الصحيحة
                print("التشخيص: بدء إنشاء نافذة بطاقة التلميذ...")
                self.active_student_window = StudentCardWindow(
                    external_db=self.db,
                    external_academic_year=self.current_academic_year
                )
                print("التشخيص: تم إنشاء النافذة بنجاح")
                
                # تعيين رمز الطالب الحالي
                self.active_student_window.current_record_id = code
                print(f"التشخيص: تم تعيين رمز الطالب: {code}")
                
                # تحميل بيانات الطالب
                print("التشخيص: محاولة تحميل بيانات الطالب...")
                self.active_student_window.load_student_data()
                print("التشخيص: تم تحميل البيانات، عرض النافذة...")
                
                # فتح النافذة في كامل الشاشة
                self.active_student_window.showMaximized()
                print("التشخيص: تم عرض النافذة في كامل الشاشة بنجاح")
                
            except Exception as e:
                print(f"التشخيص: خطأ في إنشاء النافذة: {str(e)}")
                import traceback
                traceback.print_exc()
                self.show_enhanced_message(
                    "error",
                    "خطأ",
                    f"حدث خطأ في فتح نافذة بيانات التلميذ:\n{str(e)}"
                )
        else:
            print(f"التشخيص: المشكلة - code: {code}, StudentCardWindow: {StudentCardWindow}")
            self.show_enhanced_message(
                "error",
                "خطأ",
                "لم يتم العثور على رمز التلميذ أو نافذة البيانات غير متوفرة"
            )

    def show_absence_justification(self):
        """فتح نافذة مسك تبرير الغياب للتلميذ المحدد"""
        # التحقق من وجود تلميذ محدد
        checked_rows = self.checkbox_delegate.get_checked_rows()
        if not checked_rows:
            self.show_enhanced_message(
                "warning",
                "تنبيه",
                "يرجى تحديد تلميذ واحد على الأقل لعرض سجلاته"
            )
            return

        # أخذ أول تلميذ محدد
        row = checked_rows[0]
        code_index = self.model_lists.index(row, 2)  # الرمز في العمود 2
        name_index = self.model_lists.index(row, 3)  # الاسم في العمود 3
        
        code = self.model_lists.data(code_index, Qt.DisplayRole)
        name = self.model_lists.data(name_index, Qt.DisplayRole)

        if code:
            try:
                # استيراد نافذة السجلات الجديدة
                from sub11_window_html import StudentRecordsWindow
                
                print(f"فتح نافذة سجلات التلميذ: {name} (رمز: {code})")
                print(f"استخدام اتصال قاعدة البيانات المباشر")
                
                # إنشاء وعرض نافذة السجلات باستخدام اتصال قاعدة البيانات المباشر
                self.records_window = StudentRecordsWindow(
                    student_code=code,
                    student_name=name or "غير محدد",
                    db=self.db,  # استخدام اتصال قاعدة البيانات المباشر
                    parent=self
                )
                self.records_window.show()
                
                # التأكد من فتح النافذة في كامل الشاشة
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(100, self.records_window.showMaximized)
                
                print(f"✅ تم فتح نافذة سجلات التلميذ في كامل الشاشة: {code} - {name}")
                
            except ImportError as e:
                print(f"خطأ في استيراد نافذة السجلات: {e}")
                self.show_enhanced_message(
                    "error",
                    "خطأ",
                    "فشل في تحميل نافذة السجلات\n\nتأكد من وجود ملف sub11_window_html.py"
                )
            except Exception as e:
                print(f"خطأ في عرض سجلات التلميذ: {e}")
                self.show_enhanced_message(
                    "error",
                    "خطأ",
                    f"حدث خطأ في فتح نافذة السجلات:\n{str(e)}"
                )



    def closeEvent(self, event):
        if self.db:
            # إغلاق الاتصال بقاعدة البيانات
            self.db.close()
        event.accept()












if __name__ == '__main__':
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    connected, db_connection, db_error = create_connection()
    if not connected:
        error_widget = QWidget(); error_layout = QVBoxLayout(error_widget)
        error_label = QLabel(f"فشل الاتصال بقاعدة البيانات:\n{db_error}"); error_label.setAlignment(Qt.AlignCenter); error_label.setStyleSheet("color: red; font-size: 16px; padding: 20px;")
        error_layout.addWidget(error_label); error_widget.setWindowTitle("خطأ في قاعدة البيانات"); error_widget.resize(400, 150); error_widget.show()
        exit_code_err = app.exec_(); sys.exit(exit_code_err)

    current_year, year_error = get_current_academic_year(db_connection)
    if year_error:
        print(f"تحذير: {year_error}")
        # استخدام سنة دراسية افتراضية إذا لم يتم العثور على سنة في قاعدة البيانات
        current_year = "2024-2025"
        print(f"تم استخدام السنة الدراسية الافتراضية: {current_year}")

    try:
        # استخدام الفئة Sub4Window من الملف الحالي
        main_window = Sub4Window(db=db_connection, academic_year=current_year)
        main_window.show()
    except Exception as e:
        # استخدام رسالة خطأ بسيطة للحالات الحرجة
        QMessageBox.critical(None, "خطأ فادح", f"فشل في تشغيل النافذة الرئيسية:\n{e}")
        if db_connection and db_connection.isOpen():
            conn_name = db_connection.connectionName()
            db_connection.close()
            # QSqlDatabase.removeDatabase(conn_name) # Keep removed
        sys.exit(1)

    exit_code = app.exec_()
    sys.exit(exit_code)