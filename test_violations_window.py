#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار لنافذة المخالفات المحدثة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# إضافة المسار الحالي للنظام
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد نافذة المخالفات
from sub12_window_html import StudentViolationsWindow

def main():
    """تشغيل تطبيق اختبار نافذة المخالفات"""
    app = QApplication(sys.argv)
    
    # ضبط إعدادات التطبيق
    app.setLayoutDirection(Qt.RightToLeft)
    app.setApplicationName("اختبار نافذة المخالفات")
    app.setApplicationVersion("1.0")
    
    # إنشاء النافذة
    print("🚀 إنشاء نافذة المخالفات...")
    window = StudentViolationsWindow(
        student_code="12345",
        student_name="أحمد محمد علي",
        db_path="data.db"
    )
    
    # عرض النافذة
    print("📱 عرض النافذة...")
    window.show()
    
    # التأكد من فتحها بكامل الشاشة بعد قليل
    from PyQt5.QtCore import QTimer
    QTimer.singleShot(200, lambda: window.open_fullscreen())
    
    print("✅ تم فتح نافذة المخالفات!")
    print("🖥️ النافذة ستفتح في كامل الشاشة تلقائياً")
    print("� تم إضافة حقل الأستاذ الذي يتحدث تلقائياً عند اختيار المادة")
    print("�💡 استخدم Alt+F4 أو زر الإغلاق للخروج")
    
    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
