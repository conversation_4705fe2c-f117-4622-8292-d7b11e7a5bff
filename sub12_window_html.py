#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نافذة مسك المخالفات - النسخة الحديثة باستخدام Python + HTML
محولة من sub11_window.py لتستخدم منهجية Python + HTML الحديثة
"""

import sys
import os
import sqlite3
import json
from datetime import datetime
import json
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtSql import *
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

# استيراد الوحدات المطلوبة
try:
    from custom_messages import show_custom_message
except ImportError:
    def show_custom_message(parent, message, title, msg_type):
        if msg_type == "warning":
            QMessageBox.warning(parent, title, message)
        elif msg_type == "error":
            QMessageBox.critical(parent, title, message)
        else:
            QMessageBox.information(parent, title, message)

try:
    from database_utils import get_db_manager
except ImportError:
    def get_db_manager(db_path="data.db"):
        """إنشاء مدير قاعدة بيانات بسيط إذا لم تكن وحدة database_utils متوفرة"""
        try:
            import sqlite3
            import os
            
            # التحقق من وجود ملف قاعدة البيانات
            if not os.path.exists(db_path):
                print(f"ملف قاعدة البيانات غير موجود: {db_path}")
                return None
            
            class SimpleDatabaseManager:
                def __init__(self, db_path):
                    self.db_path = db_path
                
                def execute_query(self, query, params=None):
                    """تنفيذ استعلام قاعدة البيانات"""
                    try:
                        conn = sqlite3.connect(self.db_path)
                        cursor = conn.cursor()
                        
                        if params:
                            cursor.execute(query, params)
                        else:
                            cursor.execute(query)
                        
                        if query.strip().upper().startswith('SELECT'):
                            result = cursor.fetchall()
                        else:
                            conn.commit()
                            result = cursor.rowcount
                        
                        conn.close()
                        return True, result, None
                        
                    except Exception as e:
                        print(f"خطأ في تنفيذ الاستعلام: {e}")
                        return False, None, str(e)
            
            return SimpleDatabaseManager(db_path)
            
        except Exception as e:
            print(f"خطأ في إنشاء مدير قاعدة البيانات: {e}")
            return None

# تصدير الكلاسات المتاحة للاستيراد
__all__ = ['StudentViolationsWindow']

class StudentViolationsWindow(QMainWindow):
    """نافذة مسك المخالفات - تستخدم منهجية Python + HTML الحديثة"""
    
    def __init__(self, student_code=None, student_name=None, db_path="data.db", parent=None):
        super().__init__(parent)
        
        # المتغيرات الأساسية
        self.student_code = student_code
        self.student_name = student_name or "غير محدد"
        self.db_path = db_path
        self.parent_window = parent
        
        # إعداد قاعدة البيانات
        print(f"🔧 إعداد مدير قاعدة البيانات...")
        print(f"📁 مسار قاعدة البيانات: {db_path}")
        self.db_manager = get_db_manager(db_path) if get_db_manager else None
        
        if self.db_manager:
            print("✅ تم إنشاء مدير قاعدة البيانات بنجاح")
        else:
            print("❌ لم يتم إنشاء مدير قاعدة البيانات")
        
        # إعداد النافذة
        self.setupUI()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
        
        # تحميل بيانات التلميذ إذا توفر
        if self.student_code:
            self.load_student_data()
        
        # إعداد جسر JavaScript للتفاعل مع الأساتذة
        self.setup_javascript_bridge()
        
        # فتح النافذة في كامل الشاشة بعد تحميل المحتوى
        QTimer.singleShot(100, self.open_fullscreen)
    
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        # إعدادات النافذة الأساسية
        self.setWindowTitle(f" مسك المخالفات - {self.student_name}")
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين الحد الأدنى لحجم النافذة
        self.setMinimumSize(1200, 800)
        
        # إضافة أيقونة للنافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #fff3e0,
                    stop: 1 #ffcc80
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # إنشاء منطقة عرض المحتوى HTML
        self.web_view = QWebEngineView()
        main_layout.addWidget(self.web_view)
        
        # إضافة أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        buttons_layout.setContentsMargins(10, 5, 10, 10)
        
        # زر الحفظ
        save_button = QPushButton("💾 حفظ المخالفة")
        save_button.setFont(QFont("Calibri", 14, QFont.Bold))
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-family: 'Calibri';
            }
            QPushButton:hover {
                background-color: #45a049;
                transform: translateY(-2px);
            }
        """)
        save_button.clicked.connect(self.save_violation)
        
        # زر المسح
        clear_button = QPushButton("🗑️ مسح البيانات")
        clear_button.setFont(QFont("Calibri", 14, QFont.Bold))
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-family: 'Calibri';
            }
            QPushButton:hover {
                background-color: #e68900;
                transform: translateY(-2px);
            }
        """)
        clear_button.clicked.connect(self.clear_form)
        
        # زر الطباعة
        print_button = QPushButton("🖨️ طباعة")
        print_button.setFont(QFont("Calibri", 14, QFont.Bold))
        print_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-family: 'Calibri';
            }
            QPushButton:hover {
                background-color: #1976D2;
                transform: translateY(-2px);
            }
        """)
        print_button.clicked.connect(self.print_violation)
        
        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.setFont(QFont("Calibri", 14, QFont.Bold))
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-family: 'Calibri';
            }
            QPushButton:hover {
                background-color: #d32f2f;
                transform: translateY(-2px);
            }
        """)
        close_button.clicked.connect(self.close)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(clear_button)
        buttons_layout.addWidget(print_button)
        buttons_layout.addWidget(close_button)
        
        main_layout.addLayout(buttons_layout)
        
        # إضافة شريط الحالة
        self.status_bar = self.statusBar()
        self.status_bar.setFont(QFont("Calibri", 12))
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                color: #333;
                border-top: 1px solid #ddd;
                font-family: 'Calibri';
                font-size: 12px;
                padding: 5px 10px;
            }
        """)
        self.status_bar.showMessage("جاهز لتسجيل المخالفات")
    
    def load_initial_data(self):
        """تحميل البيانات الأولية للنافذة"""
        try:
            # تحميل البيانات من قاعدة البيانات
            levels = self.get_levels()
            subjects = self.get_subjects()
            violations = self.get_violations()
            procedures = self.get_procedures()
            
            # إنشاء محتوى HTML وعرضه
            html_content = self.create_html_content(levels, subjects, violations, procedures)
            self.web_view.setHtml(html_content)
            
            self.status_bar.showMessage("تم تحميل البيانات الأولية بنجاح")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات الأولية: {e}")
            self.status_bar.showMessage(f"خطأ في تحميل البيانات: {str(e)}")
    
    def get_levels(self):
        """جلب المستويات من قاعدة البيانات"""
        try:
            if not self.db_manager:
                return ["الأولى ثانوي", "الثانية ثانوي", "الثالثة ثانوي"]
            
            # استعلام قاعدة البيانات
            success, result, error = self.db_manager.execute_query(
                "SELECT DISTINCT المستوى FROM البنية_التربوية ORDER BY المستوى"
            )
            
            if success and result:
                return [row[0] for row in result if row[0]]
            else:
                return ["الأولى ثانوي", "الثانية ثانوي", "الثالثة ثانوي"]
                
        except Exception as e:
            print(f"خطأ في جلب المستويات: {e}")
            return ["الأولى ثانوي", "الثانية ثانوي", "الثالثة ثانوي"]

    def get_subjects(self):
        """جلب المواد من قاعدة البيانات - نفس الجدول الذي يحتوي على الأساتذة"""
        print("📚 بدء جلب المواد من قاعدة البيانات...")
        try:
            if not self.db_manager:
                print("❌ مدير قاعدة البيانات غير متوفر")
                return []

            # استعلام قاعدة البيانات من جدول الأساتذة لضمان التطابق
            success, result, error = self.db_manager.execute_query(
                "SELECT DISTINCT المادة FROM الأساتذة ORDER BY المادة"
            )

            if success and result:
                subjects = [row[0] for row in result if row[0]]
                print(f"✅ تم جلب {len(subjects)} مادة من جدول الأساتذة: {subjects}")
                return subjects
            else:
                print(f"❌ فشل في جلب المواد من قاعدة البيانات: {error}")
                return []

        except Exception as e:
            print(f"❌ خطأ في جلب المواد: {e}")
            return []

    def get_violations(self):
        """جلب أنواع المخالفات من جدول تعديل_المسميات (ID من 7 إلى 16)"""
        print("⚠️ بدء جلب أنواع المخالفات من قاعدة البيانات...")
        try:
            if not self.db_manager:
                print("❌ تعذر الاتصال بقاعدة البيانات - لا توجد بيانات افتراضية")
                return []

            # استعلام قاعدة البيانات من جدول تعديل_المسميات للمخالفات (ID من 7 إلى 16)
            success, result, error = self.db_manager.execute_query(
                "SELECT العنوان FROM تعديل_المسميات WHERE ID BETWEEN 7 AND 16 ORDER BY ID"
            )

            if success and result:
                violations = [row[0] for row in result if row[0]]
                print(f"✅ تم جلب {len(violations)} نوع مخالفة من جدول تعديل_المسميات: {violations}")
                return violations
            else:
                print(f"❌ فشل في جلب المخالفات من قاعدة البيانات: {error}")
                return []

        except Exception as e:
            print(f"❌ خطأ في جلب المخالفات: {e}")
            return []

    def get_procedures(self):
        """جلب الإجراءات المتخذة من جدول تعديل_المسميات (ID من 17 إلى 20)"""
        print("📋 بدء جلب الإجراءات المتخذة من قاعدة البيانات...")
        try:
            if not self.db_manager:
                print("❌ تعذر الاتصال بقاعدة البيانات - لا توجد بيانات افتراضية")
                return []

            # استعلام قاعدة البيانات من جدول تعديل_المسميات للإجراءات (ID من 17 إلى 20)
            success, result, error = self.db_manager.execute_query(
                "SELECT العنوان FROM تعديل_المسميات WHERE ID BETWEEN 17 AND 20 ORDER BY ID"
            )

            if success and result:
                procedures = [row[0] for row in result if row[0]]
                print(f"✅ تم جلب {len(procedures)} إجراء من جدول تعديل_المسميات: {procedures}")
                return procedures
            else:
                print(f"❌ فشل في جلب الإجراءات من قاعدة البيانات: {error}")
                return []

        except Exception as e:
            print(f"❌ خطأ في جلب الإجراءات: {e}")
            return []

    def get_teachers(self):
        """جلب الأساتذة من قاعدة البيانات"""
        try:
            if not self.db_manager:
                return []

            # استعلام قاعدة البيانات
            success, result, error = self.db_manager.execute_query(
                "SELECT DISTINCT اسم_الأستاذ FROM الأساتذة ORDER BY اسم_الأستاذ"
            )

            if success and result:
                return [row[0] for row in result if row[0]]
            else:
                return []

        except Exception as e:
            print(f"خطأ في جلب الأساتذة: {e}")
            return []

    def update_teachers_for_subject(self, subject):
        """تحديث قائمة الأساتذة بناءً على المادة المحددة"""
        print(f"👨‍🏫 بدء تحديث قائمة الأساتذة للمادة: {subject}")
        try:
            if not subject or not self.db_manager:
                print("❌ لا توجد مادة محددة أو مدير قاعدة البيانات غير متوفر")
                # إذا لم تكن هناك مادة محددة أو لا يوجد مدير قاعدة بيانات، قم بمسح القائمة
                js_code = """
                const teacherSelect = document.getElementById('teacher');
                teacherSelect.innerHTML = '<option value="">اختر الأستاذ</option>';
                """
                self.web_view.page().runJavaScript(js_code)
                return []

            # استعلام قاعدة البيانات لجلب الأساتذة حسب المادة المحددة
            success, result, error = self.db_manager.execute_query(
                "SELECT DISTINCT اسم_الأستاذ FROM الأساتذة WHERE المادة = ? ORDER BY اسم_الأستاذ",
                (subject,)
            )

            if success and result:
                teachers = [row[0] for row in result if row[0]]
                print(f"✅ تم العثور على {len(teachers)} أستاذ للمادة '{subject}': {teachers}")
                
                # تحديث قائمة الأساتذة في HTML
                teachers_json = json.dumps(teachers)
                js_code = f"""
                const teacherSelect = document.getElementById('teacher');
                teacherSelect.innerHTML = '<option value="">اختر الأستاذ</option>';
                
                const teachers = {teachers_json};
                teachers.forEach(teacher => {{
                    const option = document.createElement('option');
                    option.value = teacher;
                    option.textContent = teacher;
                    teacherSelect.appendChild(option);
                }});
                """
                
                self.web_view.page().runJavaScript(js_code)
                print(f"✅ تم تحديث قائمة الأساتذة في HTML")
                return teachers
            else:
                print(f"❌ لم يتم العثور على أساتذة للمادة: {subject} - خطأ: {error}")
                # مسح القائمة إذا لم توجد نتائج
                js_code = """
                const teacherSelect = document.getElementById('teacher');
                teacherSelect.innerHTML = '<option value="">لا يوجد أساتذة لهذه المادة</option>';
                """
                self.web_view.page().runJavaScript(js_code)
                return []

        except Exception as e:
            print(f"❌ خطأ في تحديث الأساتذة: {e}")
            # في حالة وجود خطأ، قم بمسح القائمة
            js_code = """
            const teacherSelect = document.getElementById('teacher');
            teacherSelect.innerHTML = '<option value="">حدث خطأ في تحميل الأساتذة</option>';
            """
            self.web_view.page().runJavaScript(js_code)
            return []

    def create_html_content(self, levels, subjects, violations, procedures):
        """إنشاء محتوى HTML للنافذة"""
        # جلب الأساتذة أيضاً
        teachers = self.get_teachers()
        
        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>مسك المخالفات</title>
            <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
            <style>
                :root {{
                    --primary-color: #667eea;
                    --secondary-color: #764ba2;
                    --success-color: #4CAF50;
                    --warning-color: #ff9800;
                    --danger-color: #f44336;
                    --info-color: #2196F3;
                    --light-gray: #f8f9fa;
                    --dark-gray: #343a40;
                    --border-radius: 12px;
                    --shadow: 0 4px 20px rgba(0,0,0,0.1);
                    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }}

                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                body {{
                    font-family: 'Calibri', 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
                    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
                    min-height: 100vh;
                    padding: 15px;
                    direction: rtl;
                    line-height: 1.6;
                }}

                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    background: white;
                    border-radius: var(--border-radius);
                    box-shadow: var(--shadow);
                    overflow: hidden;
                    animation: slideIn 0.5s ease-out;
                }}

                @keyframes slideIn {{
                    from {{
                        opacity: 0;
                        transform: translateY(30px);
                    }}
                    to {{
                        opacity: 1;
                        transform: translateY(0);
                    }}
                }}

                .header {{
                    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                    color: white;
                    padding: 25px;
                    text-align: center;
                    position: relative;
                    overflow: hidden;
                }}

                .header::before {{
                    content: '';
                    position: absolute;
                    top: -50%;
                    left: -50%;
                    width: 200%;
                    height: 200%;
                    background: repeating-linear-gradient(
                        45deg,
                        transparent,
                        transparent 2px,
                        rgba(255,255,255,0.1) 2px,
                        rgba(255,255,255,0.1) 4px
                    );
                    animation: movePattern 20s linear infinite;
                }}

                @keyframes movePattern {{
                    0% {{ transform: translate(-50%, -50%) rotate(0deg); }}
                    100% {{ transform: translate(-50%, -50%) rotate(360deg); }}
                }}

                .header h1 {{
                    font-family: 'Calibri', sans-serif;
                    font-size: 30px;
                    font-weight: bold;
                    margin-bottom: 10px;
                    position: relative;
                    z-index: 1;
                }}

                .header p {{
                    font-family: 'Calibri', sans-serif;
                    font-size: 18px;
                    opacity: 0.9;
                    position: relative;
                    z-index: 1;
                }}

                .form-container {{
                    padding: 30px;
                    background: var(--light-gray);
                }}

                .form-section {{
                    background: white;
                    border-radius: var(--border-radius);
                    padding: 25px;
                    margin-bottom: 20px;
                    box-shadow: var(--shadow);
                    border-left: 4px solid var(--primary-color);
                }}

                .section-title {{
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    font-family: 'Calibri', sans-serif;
                    font-size: 20px;
                    font-weight: bold;
                    color: #1a237e;
                    margin-bottom: 20px;
                    padding-bottom: 10px;
                    border-bottom: 2px solid #eee;
                }}

                .form-row {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 20px;
                    margin-bottom: 20px;
                    align-items: end;
                }}

                .form-group {{
                    position: relative;
                }}

                .form-group.full-width {{
                    grid-column: 1 / -1;
                }}

                label {{
                    display: block;
                    margin-bottom: 8px;
                    font-family: 'Calibri', sans-serif;
                    font-size: 17px;
                    font-weight: 600;
                    color: #1a237e;
                }}

                .required {{
                    color: var(--danger-color);
                }}

                input, select, textarea {{
                    width: 100%;
                    padding: 12px 15px;
                    border: 2px solid #e0e0e0;
                    border-radius: var(--border-radius);
                    font-family: 'Calibri', sans-serif;
                    font-size: 17px;
                    font-weight: 600;
                    color: #212121;
                    transition: var(--transition);
                    background: white;
                }}

                input:focus, select:focus, textarea:focus {{
                    outline: none;
                    border-color: var(--primary-color);
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                    transform: translateY(-2px);
                }}

                input:valid, select:valid {{
                    border-color: var(--success-color);
                }}

                textarea {{
                    resize: vertical;
                    min-height: 120px;
                    font-family: inherit;
                }}

                .search-btn {{
                    background: linear-gradient(135deg, var(--success-color), #45a049);
                    color: white;
                    border: none;
                    padding: 12px 20px;
                    border-radius: var(--border-radius);
                    cursor: pointer;
                    font-family: 'Calibri', sans-serif;
                    font-size: 16px;
                    font-weight: bold;
                    transition: var(--transition);
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    justify-content: center;
                    min-width: 120px;
                }}

                .search-btn:hover {{
                    transform: translateY(-3px);
                    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
                }}

                .search-btn:active {{
                    transform: translateY(0);
                }}

                .message {{
                    padding: 15px 20px;
                    border-radius: var(--border-radius);
                    margin: 15px 0;
                    display: none;
                    font-family: 'Calibri', sans-serif;
                    font-size: 16px;
                    font-weight: 500;
                    animation: messageSlide 0.3s ease-out;
                }}

                @keyframes messageSlide {{
                    from {{
                        opacity: 0;
                        transform: translateX(20px);
                    }}
                    to {{
                        opacity: 1;
                        transform: translateX(0);
                    }}
                }}

                .message.success {{
                    background: linear-gradient(135deg, #d4edda, #c3e6cb);
                    color: #155724;
                    border-left: 4px solid var(--success-color);
                }}

                .message.error {{
                    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
                    color: #721c24;
                    border-left: 4px solid var(--danger-color);
                }}

                .message.warning {{
                    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
                    color: #856404;
                    border-left: 4px solid var(--warning-color);
                }}

                .loading {{
                    display: none;
                    text-align: center;
                    padding: 20px;
                }}

                .spinner {{
                    border: 4px solid #f3f3f3;
                    border-top: 4px solid var(--primary-color);
                    border-radius: 50%;
                    width: 40px;
                    height: 40px;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 10px;
                }}

                @keyframes spin {{
                    0% {{ transform: rotate(0deg); }}
                    100% {{ transform: rotate(360deg); }}
                }}

                .student-info {{
                    background: linear-gradient(135deg, #e3f2fd, #bbdef
                    border: 2px solid #2196F3;
                    border-radius: var(--border-radius);
                    padding: 15px;
                    margin: 15px 0;
                    display: none;
                }}

                .student-info.show {{
                    display: block;
                    animation: fadeIn 0.5s ease-out;
                }}

                @keyframes fadeIn {{
                    from {{ opacity: 0; }}
                    to {{ opacity: 1; }}
                }}

                .input-icon {{
                    position: relative;
                }}

                .input-icon i {{
                    position: absolute;
                    left: 12px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #888;
                    z-index: 1;
                }}

                .input-icon input {{
                    padding-left: 40px;
                }}

                /* تحسينات الاستجابة */
                @media (max-width: 768px) {{
                    .container {{
                        margin: 10px;
                        border-radius: 8px;
                    }}

                    .form-container {{
                        padding: 15px;
                    }}

                    .form-row {{
                        grid-template-columns: 1fr;
                        gap: 15px;
                    }}

                    .header h1 {{
                        font-size: 22px;
                    }}
                }}

                /* تأثيرات إضافية */
                .pulse {{
                    animation: pulse 2s infinite;
                }}

                @keyframes pulse {{
                    0% {{ box-shadow: 0 0 0 0 var(--primary-color); }}
                    70% {{ box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }}
                    100% {{ box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }}
                }}

                /* تنسيق أزرار المخالفات والإجراءات المتعددة */
                .btn-secondary {{
                    background: linear-gradient(135deg, #17a2b8, #138496);
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 6px;
                    cursor: pointer;
                    font-family: 'Calibri', sans-serif;
                    font-size: 14px;
                    font-weight: 600;
                    transition: var(--transition);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 5px;
                }}

                .btn-secondary:hover {{
                    background: linear-gradient(135deg, #138496, #117a8b);
                    transform: translateY(-2px);
                    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
                }}

                .btn-secondary:active {{
                    transform: translateY(0);
                }}

                /* تنسيق إضافي للحقول الإضافية */
                #additional_violations, #additional_procedures {{
                    animation: slideDown 0.3s ease-out;
                }}

                @keyframes slideDown {{
                    from {{
                        opacity: 0;
                        max-height: 0;
                        transform: translateY(-10px);
                    }}
                    to {{
                        opacity: 1;
                        max-height: 200px;
                        transform: translateY(0);
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1> نموذج مسك المخالفات</h1>
                    <p>تسجيل وإدارة مخالفات التلاميذ</p>
                </div>

                <div class="form-container">
                    <div id="message" class="message"></div>

                    <form id="violationForm">
                        <!-- بيانات التلميذ -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="student_code">رمز التلميذ:</label>
                                <input type="text" id="student_code" name="student_code" value="{self.student_code or ''}" required onchange="loadStudentData(this.value)">
                            </div>
                            <div class="form-group">
                                <label for="student_rt">رت:</label>
                                <input type="text" id="student_rt" name="student_rt" readonly>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="student_name">اسم التلميذ:</label>
                                <input type="text" id="student_name" name="student_name" value="{self.student_name if self.student_name != 'غير محدد' else ''}" readonly>
                            </div>
                            <div class="form-group">
                                <label for="level">المستوى:</label>
                                <input type="text" id="level" name="level" readonly>
                            </div>
                            <div class="form-group">
                                <label for="section">القسم:</label>
                                <input type="text" id="section" name="section" readonly>
                            </div>
                        </div>

                        <!-- تفاصيل المخالفة -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="violation_date">تاريخ المخالفة:</label>
                                <input type="date" id="violation_date" name="violation_date" value="{datetime.now().strftime('%Y-%m-%d')}" required>
                            </div>
                            <div class="form-group">
                                <label for="violation_time">وقت المخالفة:</label>
                                <input type="time" id="violation_time" name="violation_time" value="{datetime.now().strftime('%H:%M')}" required>
                            </div>
                            <div class="form-group">
                                <label for="subject">المادة:</label>
                                <select id="subject" name="subject" onchange="updateTeachers(this.value)">
                                    <option value="">اختر المادة</option>
                                    {''.join([f'<option value="{subject}">{subject}</option>' for subject in subjects])}
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="teacher">الأستاذ:</label>
                                <select id="teacher" name="teacher">
                                    <option value="">اختر الأستاذ</option>
                                    {''.join([f'<option value="{teacher}">{teacher}</option>' for teacher in teachers])}
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="violation_type">نوع المخالفة:</label>
                                <select id="violation_type" name="violation_type">
                                    <option value="">اختر نوع المخالفة</option>
                                    {''.join([f'<option value="{violation}">{violation}</option>' for violation in violations])}
                                </select>
                                <button type="button" id="add_violation" class="btn-secondary" style="margin-top: 10px; width: 100%;">
                                    ➕ إضافة مخالفة أخرى
                                </button>
                            </div>
                            <div class="form-group">
                                <label for="procedure">الإجراء المتخذ:</label>
                                <select id="procedure" name="procedure">
                                    <option value="">اختر الإجراء</option>
                                    {''.join([f'<option value="{procedure}">{procedure}</option>' for procedure in procedures])}
                                </select>
                                <button type="button" id="add_procedure" class="btn-secondary" style="margin-top: 10px; width: 100%;">
                                    ➕ إضافة إجراء آخر
                                </button>
                            </div>
                        </div>

                        <div class="form-row" id="additional_violations" style="display: none;">
                            <div class="form-group full-width">
                                <label for="violations_text">مخالفات إضافية:</label>
                                <textarea id="violations_text" name="violations_text" placeholder="اكتب المخالفات الإضافية، كل مخالفة في سطر منفصل..." rows="4"></textarea>
                            </div>
                        </div>

                        <div class="form-row" id="additional_procedures" style="display: none;">
                            <div class="form-group full-width">
                                <label for="procedures_text">إجراءات إضافية:</label>
                                <textarea id="procedures_text" name="procedures_text" placeholder="اكتب الإجراءات الإضافية، كل إجراء في سطر منفصل..." rows="4"></textarea>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group full-width">
                                <label for="description">وصف المخالفة:</label>
                                <textarea id="description" name="description" placeholder="اكتب وصفاً مفصلاً للمخالفة..." required></textarea>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group full-width">
                                <label for="notes">ملاحظات إضافية:</label>
                                <textarea id="notes" name="notes" placeholder="أي ملاحظات إضافية..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <script>
                // دالة تحميل بيانات التلميذ
                function loadStudentData(studentCode) {{
                    if (!studentCode) {{
                        // مسح البيانات إذا كان الحقل فارغاً
                        const elements = ['student_rt', 'student_name', 'level', 'section'];
                        elements.forEach(id => {{
                            const element = document.getElementById(id);
                            if (element) element.value = '';
                        }});
                        return;
                    }}

                    // إرسال طلب البحث إلى Python
                    window.loadStudentDataRequest = studentCode;
                    console.log(`طلب تحميل بيانات التلميذ: ${{studentCode}}`);
                }}

                // دالة البحث عن التلميذ (محذوفة - لا نحتاجها بعد الآن)
                // function searchStudent() {{ ... }}

                // دالة تحديث الأقسام حسب المستوى (محذوفة - الآن الحقول readonly)
                // function updateSections(level, selectedSection = '') {{ ... }}

                // دالة تحديث الأساتذة حسب المادة المحددة
                function updateTeachers(subject, selectedTeacher = '') {{
                    const teacherSelect = document.getElementById('teacher');
                    if (!teacherSelect) {{
                        console.error('عنصر teacher غير موجود');
                        return;
                    }}
                    
                    teacherSelect.innerHTML = '<option value="">جاري التحميل...</option>';

                    if (subject) {{
                        // إرسال طلب لـ Python لجلب الأساتذة حسب المادة من قاعدة البيانات
                        window.updateTeachersForSubject = subject;
                        console.log(`طلب تحديث الأساتذة للمادة: ${{subject}}`);
                    }} else {{
                        // إذا لم تكن هناك مادة محددة، اعرض رسالة فارغة
                        teacherSelect.innerHTML = '<option value="">اختر المادة أولاً</option>';
                    }}
                }}

                // دالة عرض الرسائل
                function showMessage(message, type) {{
                    const messageDiv = document.getElementById('message');
                    messageDiv.textContent = message;
                    messageDiv.className = `message ${{type}}`;
                    messageDiv.style.display = 'block';

                    setTimeout(() => {{
                        messageDiv.style.display = 'none';
                    }}, 5000);
                }}

                // دالة مسح النموذج
                function clearForm() {{
                    const form = document.getElementById('violationForm');
                    if (form) form.reset();
                    
                    // إعادة تعيين القيم الافتراضية مع التحقق من وجود العناصر
                    const studentCodeElement = document.getElementById('student_code');
                    if (studentCodeElement) studentCodeElement.value = '{self.student_code or ''}';
                    
                    const studentNameElement = document.getElementById('student_name');
                    if (studentNameElement) studentNameElement.value = '{self.student_name if self.student_name != 'غير محدد' else ''}';
                    
                    const violationDateElement = document.getElementById('violation_date');
                    if (violationDateElement) violationDateElement.value = '{datetime.now().strftime('%Y-%m-%d')}';
                    
                    const violationTimeElement = document.getElementById('violation_time');
                    if (violationTimeElement) violationTimeElement.value = '{datetime.now().strftime('%H:%M')}';
                    
                    // إعادة تعيين قائمة الأساتذة
                    const teacherSelect = document.getElementById('teacher');
                    if (teacherSelect) teacherSelect.innerHTML = '<option value="">اختر الأستاذ</option>';
                    
                    // إخفاء وإعادة تعيين صناديق النص الإضافية
                    const additionalViolations = document.getElementById('additional_violations');
                    const additionalProcedures = document.getElementById('additional_procedures');
                    const violationsText = document.getElementById('violations_text');
                    const proceduresText = document.getElementById('procedures_text');
                    
                    if (additionalViolations) additionalViolations.style.display = 'none';
                    if (additionalProcedures) additionalProcedures.style.display = 'none';
                    if (violationsText) violationsText.value = '';
                    if (proceduresText) proceduresText.value = '';
                    
                    showMessage('تم مسح البيانات', 'success');
                }}

                // دالة إضافة مخالفة إضافية
                function addAdditionalViolation() {{
                    const violationSelect = document.getElementById('violation_type');
                    const violationsText = document.getElementById('violations_text');
                    const additionalViolationsDiv = document.getElementById('additional_violations');
                    
                    if (!violationSelect || !violationsText || !additionalViolationsDiv) {{
                        console.error('بعض عناصر المخالفات غير موجودة');
                        return;
                    }}
                    
                    if (violationSelect.value) {{
                        // إظهار مربع النص الإضافي
                        additionalViolationsDiv.style.display = 'block';
                        
                        // إضافة المخالفة المحددة لمربع النص
                        let currentText = violationsText.value;
                        if (currentText && !currentText.endsWith('\\n')) {{
                            currentText += '\\n';
                        }}
                        violationsText.value = currentText + violationSelect.options[violationSelect.selectedIndex].text;
                        
                        // إعادة تعيين القائمة المنسدلة
                        violationSelect.value = '';
                        
                        showMessage('تم إضافة المخالفة', 'success');
                    }} else {{
                        showMessage('يرجى اختيار نوع المخالفة أولاً', 'warning');
                    }}
                }}

                // دالة إضافة إجراء إضافي
                function addAdditionalProcedure() {{
                    const procedureSelect = document.getElementById('procedure');
                    const proceduresText = document.getElementById('procedures_text');
                    const additionalProceduresDiv = document.getElementById('additional_procedures');
                    
                    if (!procedureSelect || !proceduresText || !additionalProceduresDiv) {{
                        console.error('بعض عناصر الإجراءات غير موجودة');
                        return;
                    }}
                    
                    if (procedureSelect.value) {{
                        // إظهار مربع النص الإضافي
                        additionalProceduresDiv.style.display = 'block';
                        
                        // إضافة الإجراء المحدد لمربع النص
                        let currentText = proceduresText.value;
                        if (currentText && !currentText.endsWith('\\n')) {{
                            currentText += '\\n';
                        }}
                        proceduresText.value = currentText + procedureSelect.options[procedureSelect.selectedIndex].text;
                        
                        // إعادة تعيين القائمة المنسدلة
                        procedureSelect.value = '';
                        
                        showMessage('تم إضافة الإجراء', 'success');
                    }} else {{
                        showMessage('يرجى اختيار الإجراء أولاً', 'warning');
                    }}
                }}

                // دالة جمع بيانات النموذج المحدثة
                function getFormData() {{
                    const form = document.getElementById('violationForm');
                    const htmlFormData = new FormData(form);
                    const data = {{}};

                    for (let [key, value] of htmlFormData.entries()) {{
                        data[key] = value;
                    }}

                    // دمج المخالفات (الأساسية + الإضافية)
                    let allViolations = [];
                    if (data.violation_type) {{
                        allViolations.push(data.violation_type);
                    }}
                    if (data.violations_text) {{
                        const additionalViolations = data.violations_text.split('\\n').filter(v => v.trim());
                        allViolations = allViolations.concat(additionalViolations);
                    }}
                    data.all_violations = allViolations.join(' | ');

                    // دمج الإجراءات (الأساسية + الإضافية)
                    let allProcedures = [];
                    if (data.procedure) {{
                        allProcedures.push(data.procedure);
                    }}
                    if (data.procedures_text) {{
                        const additionalProcedures = data.procedures_text.split('\\n').filter(p => p.trim());
                        allProcedures = allProcedures.concat(additionalProcedures);
                    }}
                    data.all_procedures = allProcedures.join(' | ');

                    return data;
                }}

                // تحميل بيانات التلميذ تلقائياً عند تحميل الصفحة
                window.addEventListener('DOMContentLoaded', function() {{
                    const studentCode = document.getElementById('student_code');
                    if (studentCode && studentCode.value) {{
                        loadStudentData(studentCode.value);
                    }}
                    
                    // ربط أحداث أزرار المخالفات والإجراءات المتعددة
                    const addViolationBtn = document.getElementById('add_violation');
                    const addProcedureBtn = document.getElementById('add_procedure');
                    
                    if (addViolationBtn) {{
                        addViolationBtn.addEventListener('click', addAdditionalViolation);
                    }} else {{
                        console.warn('زر إضافة المخالفة غير موجود');
                    }}
                    
                    if (addProcedureBtn) {{
                        addProcedureBtn.addEventListener('click', addAdditionalProcedure);
                    }} else {{
                        console.warn('زر إضافة الإجراء غير موجود');
                    }}
                    
                    // ربط حدث تغيير كود التلميذ
                    if (studentCode) {{
                        studentCode.addEventListener('input', function() {{
                            loadStudentData(this.value);
                        }});
                    }}
                    
                    // ربط حدث تغيير المادة لتحديث الأساتذة
                    const subjectSelect = document.getElementById('subject');
                    if (subjectSelect) {{
                        subjectSelect.addEventListener('change', function() {{
                            updateTeachers(this.value);
                        }});
                    }}
                }});
            </script>
        </body>
        </html>
        """

        return html_content

    def load_student_data(self):
        """تحميل بيانات التلميذ المحدد"""
        print(f"👨‍🎓 بدء تحميل بيانات التلميذ: {self.student_code}")
        try:
            if not self.student_code or not self.db_manager:
                print("❌ لا يوجد رمز تلميذ أو مدير قاعدة البيانات غير متوفر")
                return

            # البحث عن بيانات التلميذ
            success, result, error = self.db_manager.execute_query(
                """SELECT l.الرمز, s.الاسم_والنسب, l.المستوى, l.القسم, l.رت
                   FROM اللوائح l
                   JOIN السجل_العام s ON l.الرمز = s.الرمز
                   WHERE l.الرمز = ? AND l.السنة_الدراسية = ?""",
                (self.student_code, "2024/2025")
            )

            if success and result:
                student_data = {
                    'code': result[0][0],
                    'name': result[0][1],
                    'level': result[0][2],
                    'section': result[0][3],
                    'rt': result[0][4]
                }
                
                print(f"✅ تم العثور على بيانات التلميذ: {student_data}")

                # تحديث البيانات في النموذج HTML باستخدام JavaScript
                js_code = f"""
                try {{
                    const elements = [
                        ['student_code', '{student_data['code']}'],
                        ['student_rt', '{student_data['rt']}'],
                        ['student_name', '{student_data['name']}'],
                        ['level', '{student_data['level']}'],
                        ['section', '{student_data['section']}']
                    ];
                    
                    elements.forEach(([id, value]) => {{
                        const element = document.getElementById(id);
                        if (element) {{
                            element.value = value;
                        }} else {{
                            console.warn('العنصر غير موجود:', id);
                        }}
                    }});
                    
                    console.log('تم تحديث بيانات التلميذ بنجاح');
                }} catch(error) {{
                    console.error('خطأ في تحديث بيانات التلميذ:', error);
                }}
                """

                self.web_view.page().runJavaScript(js_code)
                self.status_bar.showMessage(f"تم تحميل بيانات التلميذ: {student_data['name']}")
                print(f"✅ تم تحديث البيانات في واجهة HTML")

            else:
                print(f"❌ لم يتم العثور على بيانات التلميذ '{self.student_code}' - خطأ: {error}")

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات التلميذ: {e}")
            self.status_bar.showMessage(f"خطأ في تحميل بيانات التلميذ: {str(e)}")

    def save_violation(self):
        """حفظ المخالفة في قاعدة البيانات"""
        try:
            # جمع بيانات النموذج من HTML
            js_code = """
            try {
                const violationFormData = getFormData();
                violationFormData;
            } catch(error) {
                console.error('خطأ في جمع بيانات النموذج:', error);
                null;
            }
            """

            def handle_form_data(form_data):
                try:
                    if not form_data:
                        self.show_message("لا توجد بيانات للحفظ", "error")
                        return

                    # معالجة المخالفات والإجراءات المتعددة
                    form_data = self.process_multiple_violations(form_data)

                    # التحقق من البيانات المطلوبة (تحديث للمخالفات المتعددة)
                    required_fields = ['student_code', 'student_name', 'description']
                    for field in required_fields:
                        if not form_data.get(field):
                            self.show_message(f"يرجى ملء حقل {field}", "warning")
                            return

                    # التحقق من وجود مخالفة واحدة على الأقل
                    if not form_data.get('all_violations') and not form_data.get('violation_type'):
                        self.show_message("يرجى إضافة مخالفة واحدة على الأقل", "warning")
                        return

                    # التحقق من وجود إجراء واحد على الأقل
                    if not form_data.get('all_procedures') and not form_data.get('procedure'):
                        self.show_message("يرجى إضافة إجراء واحد على الأقل", "warning")
                        return

                    # حفظ المخالفة في قاعدة البيانات
                    if self.db_manager:
                        # تجهيز التاريخ والوقت المدمجين
                        violation_datetime = f"{form_data.get('violation_date')} {form_data.get('violation_time')}"
                        
                        success, result, error = self.db_manager.execute_query(
                            """INSERT INTO المخالفات
                               (التاريخ, رمز_التلميذ, اسم_التلميذ, المستوى, القسم, المادة, الأستاذ,
                                الملاحظات, الإجراءات, تاريخ_التسجيل, السنة_الدراسية, الأسدس, رت)
                               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                            (
                                violation_datetime,  # التاريخ والوقت مدمجين
                                form_data.get('student_code'),
                                form_data.get('student_name'),
                                form_data.get('level'),
                                form_data.get('section'),
                                form_data.get('subject'),
                                form_data.get('teacher'),
                                # دمج وصف المخالفة مع المخالفات المحددة
                                f"المخالفات: {form_data.get('all_violations') or form_data.get('violation_type') or 'غير محدد'}\n\nالوصف: {form_data.get('description')}\n\nملاحظات: {form_data.get('notes') or 'لا توجد'}",
                                # استخدام الإجراءات المدمجة
                                form_data.get('all_procedures') or form_data.get('procedure'),
                                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                "2024/2025",  # السنة الدراسية
                                "الأول",      # الأسدس
                                form_data.get('student_rt') or form_data.get('rt') or ""  # رت مع احتياطي
                            )
                        )

                        if success:
                            violation_summary = f"المخالفة: {form_data.get('all_violations') or form_data.get('violation_type')}"
                            procedure_summary = f"الإجراء: {form_data.get('all_procedures') or form_data.get('procedure')}"
                            self.show_message(f"تم حفظ المخالفة بنجاح ✅\n{violation_summary}\n{procedure_summary}", "success")
                            self.status_bar.showMessage("تم حفظ المخالفة بنجاح")
                            print(f"✅ تم حفظ المخالفة للتلميذ: {form_data.get('student_name')}")
                            print(f"📝 المخالفات المحفوظة: {form_data.get('all_violations') or form_data.get('violation_type')}")
                            print(f"⚖️ الإجراءات المحفوظة: {form_data.get('all_procedures') or form_data.get('procedure')}")
                        else:
                            self.show_message(f"خطأ في حفظ المخالفة: {error}", "error")
                            print(f"❌ فشل في حفظ المخالفة: {error}")
                    else:
                        self.show_message("تم حفظ المخالفة (وضع التجريب)", "success")

                except Exception as e:
                    print(f"خطأ في معالجة بيانات النموذج: {e}")
                    self.show_message(f"خطأ في معالجة البيانات: {str(e)}", "error")

            self.web_view.page().runJavaScript(js_code, handle_form_data)

        except Exception as e:
            print(f"خطأ في حفظ المخالفة: {e}")
            self.show_message(f"خطأ في حفظ المخالفة: {str(e)}", "error")

    def clear_form(self):
        """مسح بيانات النموذج"""
        try:
            js_code = """
            clearForm();
            """
            self.web_view.page().runJavaScript(js_code)

        except Exception as e:
            print(f"خطأ في مسح النموذج: {e}")

    def show_message(self, message, msg_type="success"):
        """عرض رسالة في النافذة"""
        try:
            js_code = f"""
            showMessage('{message}', '{msg_type}');
            """
            self.web_view.page().runJavaScript(js_code)
            self.status_bar.showMessage(message)

        except Exception as e:
            print(f"خطأ في عرض الرسالة: {e}")

    def print_violation(self):
        """طباعة المخالفة"""
        try:
            # جمع بيانات النموذج للطباعة
            js_code = """
            try {
                const printFormData = getFormData();
                printFormData;
            } catch(error) {
                console.error('خطأ في جمع بيانات الطباعة:', error);
                null;
            }
            """

            def handle_print_data(form_data):
                try:
                    if not form_data or not form_data.get('student_code'):
                        self.show_message("لا توجد بيانات للطباعة", "warning")
                        return

                    # إنشاء تقرير HTML للطباعة
                    print_html = self.create_print_html(form_data)

                    # إنشاء نافذة طباعة
                    self.print_window = QWebEngineView()
                    self.print_window.setWindowTitle("طباعة المخالفة")
                    self.print_window.resize(800, 600)
                    self.print_window.setHtml(print_html)
                    self.print_window.show()

                    self.show_message("تم إعداد المخالفة للطباعة", "success")

                except Exception as e:
                    print(f"خطأ في معالجة بيانات الطباعة: {e}")
                    self.show_message(f"خطأ في الطباعة: {str(e)}", "error")

            self.web_view.page().runJavaScript(js_code, handle_print_data)

        except Exception as e:
            print(f"خطأ في طباعة المخالفة: {e}")
            self.show_message(f"خطأ في الطباعة: {str(e)}", "error")

    def create_print_html(self, form_data):
        """إنشاء تقرير HTML للطباعة"""
        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير المخالفة</title>
            <style>
                body {{
                    font-family: 'Arial', sans-serif;
                    direction: rtl;
                    margin: 20px;
                    line-height: 1.6;
                }}
                .header {{
                    text-align: center;
                    border-bottom: 3px solid #333;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .title {{
                    font-size: 24px;
                    font-weight: bold;
                    color: #d32f2f;
                    margin-bottom: 10px;
                }}
                .info-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }}
                .info-table th, .info-table td {{
                    border: 1px solid #333;
                    padding: 10px;
                    text-align: right;
                }}
                .info-table th {{
                    background-color: #f5f5f5;
                    font-weight: bold;
                }}
                .description-box {{
                    border: 2px solid #333;
                    padding: 15px;
                    margin: 20px 0;
                    background-color: #fafafa;
                }}
                .footer {{
                    margin-top: 40px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                }}
                @media print {{
                    body {{ margin: 0; }}
                    .no-print {{ display: none; }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="title">⚠️ تقرير مخالفة تلميذ</div>
                <p>تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
            </div>

            <table class="info-table">
                <tr>
                    <th>رمز التلميذ</th>
                    <td>{form_data.get('student_code', '')}</td>
                    <th>اسم التلميذ</th>
                    <td>{form_data.get('student_name', '')}</td>
                </tr>
                <tr>
                    <th>المستوى</th>
                    <td>{form_data.get('level', '')}</td>
                    <th>القسم</th>
                    <td>{form_data.get('section', '')}</td>
                </tr>
                <tr>
                    <th>تاريخ المخالفة</th>
                    <td>{form_data.get('violation_date', '')}</td>
                    <th>وقت المخالفة</th>
                    <td>{form_data.get('violation_time', '')}</td>
                </tr>
                <tr>
                    <th>المادة</th>
                    <td>{form_data.get('subject', '')}</td>
                    <th>الأستاذ</th>
                    <td>{form_data.get('teacher', '')}</td>
                </tr>
                <tr>
                    <th>نوع المخالفة</th>
                    <td>{form_data.get('all_violations') or form_data.get('violation_type', '')}</td>
                    <th>الإجراء المتخذ</th>
                    <td>{form_data.get('all_procedures') or form_data.get('procedure', '')}</td>
                </tr>
            </table>

            <div class="description-box">
                <h3>وصف المخالفة:</h3>
                <p>{form_data.get('description', '')}</p>
            </div>

            {f'<div class="description-box"><h3>ملاحظات إضافية:</h3><p>{form_data.get("notes", "")}</p></div>' if form_data.get('notes') else ''}

            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخالفات</p>
            </div>
        </body>
        </html>
        """

        return html_content

    def open_fullscreen(self):
        """فتح النافذة في كامل الشاشة"""
        try:
            # فتح النافذة في كامل الشاشة
            self.showMaximized()
            
            # التأكد من أن النافذة في المقدمة
            self.raise_()
            self.activateWindow()
            
            # تحديث شريط الحالة
            self.status_bar.showMessage("تم فتح النافذة في كامل الشاشة - جاهز لتسجيل المخالفات")
            
            print("✅ تم فتح النافذة في كامل الشاشة بنجاح")
            
        except Exception as e:
            print(f"خطأ في فتح النافذة في كامل الشاشة: {e}")
    
    def showEvent(self, event):
        """تخصيص سلوك عرض النافذة لضمان فتحها بكامل الشاشة"""
        super().showEvent(event)
        
        # التأكد من فتح النافذة في كامل الشاشة في كل مرة يتم عرضها
        if not self.isMaximized():
            QTimer.singleShot(50, self.showMaximized)

    def resizeEvent(self, event):
        """معالجة تغيير حجم النافذة للحفاظ على الشاشة الكاملة"""
        super().resizeEvent(event)
        
        # إذا لم تكن النافذة في وضع كامل الشاشة، أعدها إلى هذا الوضع
        if not self.isMaximized() and self.isVisible():
            QTimer.singleShot(100, self.showMaximized)

    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح - إضافة اختصارات مفيدة"""
        try:
            # F11 للتبديل بين كامل الشاشة والنافذة العادية
            if event.key() == Qt.Key_F11:
                if self.isMaximized():
                    self.showNormal()
                else:
                    self.showMaximized()
                return
            
            # Ctrl+S للحفظ
            elif event.key() == Qt.Key_S and event.modifiers() == Qt.ControlModifier:
                self.save_violation()
                return
            
            # Ctrl+P للطباعة
            elif event.key() == Qt.Key_P and event.modifiers() == Qt.ControlModifier:
                self.print_violation()
                return
            
            # Ctrl+N لمسح النموذج (جديد)
            elif event.key() == Qt.Key_N and event.modifiers() == Qt.ControlModifier:
                self.clear_form()
                return
            
            # Escape للإغلاق
            elif event.key() == Qt.Key_Escape:
                if self.confirm_arabic_dialog("هل تريد إغلاق نافذة المخالفات؟", "تأكيد الإغلاق"):
                    self.close()
                return
        
        except Exception as e:
            print(f"خطأ في معالجة ضغط المفاتيح: {e}")
        
        # تمرير الحدث للكلاس الأب
        super().keyPressEvent(event)

    # دوال مساعدة للرسائل والتفاعل بالعربية
    def show_arabic_message(self, message, title="رسالة", msg_type="info"):
        """عرض رسالة تأكيد باللغة العربية"""
        try:
            if msg_type == "success":
                icon = QMessageBox.Information 
                title = title or "نجح الأمر"
            elif msg_type == "warning":
                icon = QMessageBox.Warning
                title = title or "تنبيه"
            elif msg_type == "error":
                icon = QMessageBox.Critical
                title = title or "خطأ"
            else:
                icon = QMessageBox.Information
                title = title or "معلومات"
            
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setIcon(icon)
            msg_box.setLayoutDirection(Qt.RightToLeft)
            msg_box.setFont(QFont("Calibri", 12))
            
            # تخصيص النمط
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: white;
                    font-family: 'Calibri';
                    font-size: 14px;
                }
                QMessageBox QLabel {
                    color: #333;
                    font-family: 'Calibri';
                    font-size: 14px;
                }
                QMessageBox QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    font-family: 'Calibri';
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #45a049;
                }
            """)
            
            # تعيين نص الأزرار بالعربية
            if icon == QMessageBox.Information:
                msg_box.setStandardButtons(QMessageBox.Ok)
                msg_box.button(QMessageBox.Ok).setText("موافق")
            elif icon == QMessageBox.Warning or icon == QMessageBox.Critical:
                msg_box.setStandardButtons(QMessageBox.Ok)
                msg_box.button(QMessageBox.Ok).setText("موافق")
            
            msg_box.exec_()
            
        except Exception as e:
            print(f"خطأ في عرض الرسالة: {e}")

    def confirm_arabic_dialog(self, message, title="تأكيد العملية"):
        """حوار تأكيد باللغة العربية"""
        try:
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setIcon(QMessageBox.Question)
            msg_box.setLayoutDirection(Qt.RightToLeft)
            msg_box.setFont(QFont("Calibri", 12))
            
            # إضافة أزرار مخصصة بالعربية
            yes_button = msg_box.addButton("نعم", QMessageBox.YesRole)
            no_button = msg_box.addButton("لا", QMessageBox.NoRole)
            
            # تخصيص النمط
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: white;
                    font-family: 'Calibri';
                }
                QMessageBox QLabel {
                    color: #333;
                    font-family: 'Calibri';
                    font-size: 14px;
                }
                QMessageBox QPushButton {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    font-family: 'Calibri';
                    font-size: 12px;
                    font-weight: bold;
                    min-width: 80px;
                }
                QMessageBox QPushButton[text="نعم"] {
                    background-color: #4CAF50;
                    color: white;
                }
                QMessageBox QPushButton[text="نعم"]:hover {
                    background-color: #45a049;
                }
                QMessageBox QPushButton[text="لا"] {
                    background-color: #f44336;
                    color: white;
                }
                QMessageBox QPushButton[text="لا"]:hover {
                    background-color: #d32f2f;
                }
            """)
            
            msg_box.exec_();
            return msg_box.clickedButton() == yes_button;
            
        except Exception as e:
            print(f"خطأ في حوار التأكيد: {e}")
            return False

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة مع التأكيد بالعربية"""
        try:
            # طلب تأكيد الإغلاق
            if self.confirm_arabic_dialog(
                "هل تريد إغلاق نافذة مسك المخالفات؟\n\nتأكد من حفظ أي بيانات مهمة قبل الإغلاق.",
                "تأكيد إغلاق النافذة"
            ):
                print("👋 إغلاق نافذة المخالفات...")
                event.accept()
            else:
                event.ignore()
                
        except Exception as e:
            print(f"خطأ في معالجة إغلاق النافذة: {e}")
            event.accept()  # إغلاق في حالة وجود خطأ

    def save_to_database(self, form_data):
        """حفظ بيانات المخالفة في قاعدة البيانات"""
        try:
            if not self.db_manager:
                return False

            # تحضير البيانات للحفظ مع المخالفات والإجراءات المتعددة
            violation_data = {
                "date": form_data.get('violation_date'),
                "student_code": form_data.get('student_code'),
                "student_name": form_data.get('student_name'),
                "level": form_data.get('level'),
                "section": form_data.get('section'),
                "subject": form_data.get('subject'),
                "teacher": form_data.get('teacher'),
                # استخدام المخالفات والإجراءات المدمجة إذا توفرت، وإلا استخدام الأساسية
                "violation_type": form_data.get('all_violations') or form_data.get('violation_type'),
                "procedure": form_data.get('all_procedures') or form_data.get('procedure'),
                "description": form_data.get('description'),
                "notes": form_data.get('notes'),
                "school_year": "2024/2025",  # يمكن جلبها من إعدادات النظام
                "semester": "الأول"  # يمكن جلبها من إعدادات النظام
            }

            print(f"📝 حفظ المخالفة - المخالفات: {violation_data['violation_type']}")
            print(f"📝 حفظ المخالفة - الإجراءات: {violation_data['procedure']}")

            # استخدام طريقة حفظ المخالفة في مدير قاعدة البيانات
            success = self.db_manager.save_violation(violation_data)
            return success

        except Exception as e:
            print(f"خطأ في حفظ البيانات في قاعدة البيانات: {e}")
            return False

    def setup_javascript_bridge(self):
        """إعداد الجسر بين JavaScript و Python"""
        try:
            # إعداد معالج للاستجابة لطلبات JavaScript
            def handle_js_requests():
                # فحص طلبات تحديث الأساتذة
                js_code_teachers = """
                if (window.updateTeachersForSubject) {{
                    const subject = window.updateTeachersForSubject;
                    window.updateTeachersForSubject = null;
                    subject;
                }} else {{
                    null;
                }}
                """
                
                def check_teacher_update(subject):
                    if subject:
                        print(f"طلب تحديث الأساتذة للمادة: {subject}")
                        self.update_teachers_for_subject(subject)
                
                self.web_view.page().runJavaScript(js_code_teachers, check_teacher_update)
                
                # فحص طلبات تحميل بيانات التلميذ
                js_code_student = """
                if (window.loadStudentDataRequest) {{
                    const studentCode = window.loadStudentDataRequest;
                    window.loadStudentDataRequest = null;
                    studentCode;
                }} else {{
                    null;
                }}
                """
                
                def check_student_load(student_code):
                    if student_code:
                        print(f"طلب تحميل بيانات التلميذ: {student_code}")
                        self.student_code = student_code
                        self.load_student_data()
                
                self.web_view.page().runJavaScript(js_code_student, check_student_load)
            
            # تشغيل فحص دوري كل ثانية
            from PyQt5.QtCore import QTimer
            self.js_timer = QTimer()
            self.js_timer.timeout.connect(handle_js_requests)
            self.js_timer.start(1000)  # فحص كل ثانية
            
        except Exception as e:
            print(f"خطأ في إعداد جسر JavaScript: {e}")

    def process_multiple_violations(self, form_data):
        """معالجة المخالفات والإجراءات المتعددة لضمان التوافق مع النظام القديم"""
        try:
            # معالجة المخالفات
            violations = []
            if form_data.get('violation_type'):
                violations.append(form_data.get('violation_type'))
            
            if form_data.get('violations_text'):
                additional_violations = [v.strip() for v in form_data.get('violations_text').split('\n') if v.strip()]
                violations.extend(additional_violations)
            
            # معالجة الإجراءات
            procedures = []
            if form_data.get('procedure'):
                procedures.append(form_data.get('procedure'))
            
            if form_data.get('procedures_text'):
                additional_procedures = [p.strip() for p in form_data.get('procedures_text').split('\n') if p.strip()]
                procedures.extend(additional_procedures)
            
            # دمج النتائج
            form_data['all_violations'] = ' | '.join(violations) if violations else ''
            form_data['all_procedures'] = ' | '.join(procedures) if procedures else ''
            
            print(f"✅ تم معالجة المخالفات: {form_data['all_violations']}")
            print(f"✅ تم معالجة الإجراءات: {form_data['all_procedures']}")
            
            return form_data
            
        except Exception as e:
            print(f"❌ خطأ في معالجة المخالفات المتعددة: {e}")
            return form_data
