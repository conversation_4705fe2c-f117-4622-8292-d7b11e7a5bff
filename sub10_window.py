#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import traceback
import json
from datetime import datetime, date
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtSql import *
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel

# تم إزالة جميع الاستيرادات الخارجية لجعل الملف مستقلاً

class WebBridge(QObject):
    """جسر الاتصال بين Python و JavaScript"""
    
    # إشارات لـ JavaScript
    dataLoaded = pyqtSignal(str)  # إرسال البيانات كـ JSON
    saveResult = pyqtSignal(bool, str)  # نتيجة الحفظ
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = None
    
    def set_main_window(self, main_window):
        """تعيين النافذة الرئيسية"""
        self.main_window = main_window
    
    @pyqtSlot(str, str)
    def save_student_data(self, phone1, phone2, notes=""):
        """حفظ بيانات التلميذ من JavaScript"""
        try:
            if not self.main_window or not self.main_window.current_record_id:
                self.saveResult.emit(False, "لا يوجد طالب محدد للحفظ")
                return

            query = QSqlQuery(self.main_window.db)
            query.prepare("""
                UPDATE السجل_العام
                SET الهاتف_الأول = ?, الهاتف_الثاني = ?, ملاحظات = ?
                WHERE الرمز = ?
            """)
            query.addBindValue(phone1)
            query.addBindValue(phone2)
            query.addBindValue(notes)
            query.addBindValue(self.main_window.current_record_id)

            if query.exec_():
                self.saveResult.emit(True, "تم حفظ التغييرات بنجاح")
            else:
                self.saveResult.emit(False, f"فشل في حفظ التغييرات: {query.lastError().text()}")

        except Exception as e:
            self.saveResult.emit(False, f"خطأ في حفظ البيانات: {str(e)}")
    
    @pyqtSlot()
    def refresh_data(self):
        """تحديث البيانات من JavaScript"""
        if self.main_window:
            self.main_window.load_student_data()
    
    @pyqtSlot(str)
    def show_message(self, message):
        """عرض رسالة من JavaScript"""
        if self.main_window:
            QMessageBox.information(self.main_window, "رسالة", message)

# فحص توفر قاعدة البيانات قبل تشغيل البرنامج
def check_database_availability():
    """التحقق من وجود قاعدة البيانات ومدى إمكانية الوصول إليها"""
    db_path = "data.db"
    if not os.path.exists(db_path):
        print(f"تحذير: ملف قاعدة البيانات {db_path} غير موجود")
        return False
    
    try:
        # محاولة الاتصال بقاعدة البيانات للتأكد من صحتها
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود الجداول المطلوبة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='students'")
        if not cursor.fetchone():
            print("تحذير: جدول students غير موجود في قاعدة البيانات")
            conn.close()
            return False
        
        conn.close()
        return True
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return False

class StudentCardWindow(QMainWindow):
    """نافذة بطاقة التلميذ - باستخدام HTML/CSS/JavaScript"""
    
    def __init__(self, external_db=None, external_academic_year=None):
        super().__init__()
        
        # تخزين المعاملات الخارجية
        self.external_db = external_db
        self.external_academic_year = external_academic_year
        
        # متغيرات النافذة
        self.db = None
        self.current_record_id = None
        self.current_academic_year = None
        
        # إنشاء جسر الاتصال مع JavaScript
        self.web_bridge = WebBridge()
        self.web_bridge.set_main_window(self)
        
        # إعداد النافذة
        self.setupUI()
        
        # إعداد قاعدة البيانات والسنة الدراسية
        if self.connect_to_database():
            self.load_current_academic_year()
            # تحميل أول طالب للاختبار إذا لم يتم تمرير رمز خارجي
            if not self.current_record_id:
                print("تحميل أول طالب للاختبار...")
                self.load_first_student_for_testing()
        
    def setupUI(self):
        """إعداد واجهة المستخدم باستخدام HTML"""
        # إعداد النافذة الرئيسية
        self.setWindowTitle("")  # إزالة العنوان الرئيسي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # فتح النافذة في كامل الشاشة
        self.showMaximized()
        
        # تطبيق نمط للنافذة الرئيسية
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # إنشاء عارض الويب
        self.web_view = QWebEngineView()
        
        # إعداد قناة الويب للتواصل مع JavaScript
        self.web_channel = QWebChannel()
        self.web_channel.registerObject("bridge", self.web_bridge)
        self.web_view.page().setWebChannel(self.web_channel)
        
        # تحميل محتوى HTML
        self.load_html_content()
        
        layout.addWidget(self.web_view)

    def load_html_content(self):
        """تحميل محتوى HTML للبطاقة"""
        html_content = """
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بطاقة التلميذ</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }
        
        .card-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
        }
        
        @keyframes slideUp {
            from {
                transform: translateY(30px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
            background-size: cover;
        }
        
        .card-header h1 {
            font-size: 28px;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            color: #000000;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .card-header p {
            font-size: 18px;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            color: #000000;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .card-content {
            padding: 40px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .info-section {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 25px;
            border-right: 5px solid #667eea;
            transition: all 0.3s ease;
        }
        
        .info-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
        }
        
        .section-title {
            color: #667eea;
            font-size: 20px;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .field-group {
            margin-bottom: 20px;
        }
        
        .field-label {
            color: #000000;
            font-weight: bold;
            margin-bottom: 8px;
            font-size: 17px;
            font-family: 'Calibri', sans-serif;
        }
        
        .field-value {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 17px;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            color: #000000;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .field-value:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .field-value:read-only {
            background: #f8f9fa;
            color: #666;
        }
        
        .notes-section {
            background: #fff5f5;
            border-radius: 15px;
            padding: 25px;
            border-right: 5px solid #ff6b6b;
            margin-bottom: 30px;
        }
        
        .notes-textarea {
            width: 100%;
            min-height: 120px;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #000000;
            resize: vertical;
            transition: all 0.3s ease;
        }
        
        .notes-textarea:focus {
            outline: none;
            border-color: #ff6b6b;
            box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
        }
        
        .button-container {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 17px;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            color: #000000;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 150px;
            justify-content: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
        }
        
        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(250, 177, 160, 0.3);
        }
        
        .status-message {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-size: 17px;
            font-family: 'Calibri', sans-serif;
            font-weight: bold;
            opacity: 0;
            transition: all 0.3s ease;
        }
        
        .status-message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-message.show {
            opacity: 1;
        }
        
        .loading {
            display: none;
            text-align: center;
padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .button-container {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="card-container">
        <div class="card-header">
            <h1>📚 بطاقة التلميذ</h1>
            <p>نظام إدارة بيانات الطلاب</p>
        </div>
        
        <div class="card-content">
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>جارٍ تحميل البيانات...</p>
            </div>
            
            <div id="content" style="display: none;">
                <div class="info-grid">
                    <!-- قسم البيانات الأساسية -->
                    <div class="info-section">
                        <div class="section-title">
                            <span>🆔</span>
                            المعلومات الأساسية
                        </div>
                        <div class="field-group">
                            <div class="field-label">الرمز</div>
                            <input type="text" id="code" class="field-value" readonly>
                        </div>
                        <div class="field-group">
                            <div class="field-label">الاسم والنسب</div>
                            <input type="text" id="name" class="field-value" readonly>
                        </div>
                        <div class="field-group">
                            <div class="field-label">النوع</div>
                            <input type="text" id="gender" class="field-value" readonly>
                        </div>
                    </div>
                    
                    <!-- قسم البيانات الشخصية -->
                    <div class="info-section">
                        <div class="section-title">
                            <span>👤</span>
                            البيانات الشخصية
                        </div>
                        <div class="field-group">
                            <div class="field-label">تاريخ الازدياد</div>
                            <input type="text" id="birthDate" class="field-value" readonly>
                        </div>
                        <div class="field-group">
                            <div class="field-label">مكان الازدياد</div>
                            <input type="text" id="birthPlace" class="field-value" readonly>
                        </div>
                    </div>
                    
                    <!-- قسم المعلومات المدرسية -->
                    <div class="info-section">
                        <div class="section-title">
                            <span>🏫</span>
                            المعلومات المدرسية
                        </div>
                        <div class="field-group">
                            <div class="field-label">السنة الدراسية</div>
                            <input type="text" id="schoolYear" class="field-value" readonly>
                        </div>
                        <div class="field-group">
                            <div class="field-label">المستوى</div>
                            <input type="text" id="level" class="field-value" readonly>
                        </div>
                        <div class="field-group">
                            <div class="field-label">القسم</div>
                            <input type="text" id="class" class="field-value" readonly>
                        </div>
                        <div class="field-group">
                            <div class="field-label">الرقم الترتيبي</div>
                            <input type="text" id="rt" class="field-value" readonly>
                        </div>
                    </div>
                    
                    <!-- قسم معلومات الاتصال -->
                    <div class="info-section">
                        <div class="section-title">
                            <span>📞</span>
                            معلومات الاتصال
                        </div>
                        <div class="field-group">
                            <div class="field-label">الهاتف الأول</div>
                            <input type="text" id="phone1" class="field-value" placeholder="أدخل رقم الهاتف الأول">
                        </div>
                        <div class="field-group">
                            <div class="field-label">الهاتف الثاني</div>
                            <input type="text" id="phone2" class="field-value" placeholder="أدخل رقم الهاتف الثاني">
                        </div>
                    </div>
                </div>
                
                <!-- قسم الملاحظات -->
                <div class="notes-section">
                    <div class="section-title">
                        <span>📝</span>
                        الملاحظات
                    </div>
                    <textarea id="notes" class="notes-textarea" placeholder="أضف ملاحظاتك هنا..."></textarea>
                </div>
                
                <!-- أزرار التحكم -->
                <div class="button-container">
                    <button class="btn btn-primary" onclick="saveData()">
                        <span>💾</span>
                        حفظ التغييرات
                    </button>
                    <button class="btn btn-secondary" onclick="refreshData()">
                        <span>🔄</span>
                        تحديث البيانات
                    </button>
                </div>
                
                <div id="statusMessage" class="status-message"></div>
            </div>
        </div>
    </div>

    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <script>
        let bridge = null;
        
        // تهيئة قناة التواصل مع Python
        new QWebChannel(qt.webChannelTransport, function(channel) {
            bridge = channel.objects.bridge;
            
            // الاستماع للإشارات من Python
            bridge.dataLoaded.connect(function(jsonData) {
                loadStudentData(JSON.parse(jsonData));
            });
            
            bridge.saveResult.connect(function(success, message) {
                showMessage(success ? 'success' : 'error', message);
            });
            
            // إخفاء شاشة التحميل وعرض المحتوى
            hideLoading();
        });
        
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('content').style.display = 'block';
        }
        
        function loadStudentData(data) {
            // تحميل البيانات في الحقول
            document.getElementById('code').value = data.code || '';
            document.getElementById('name').value = data.name || '';
            document.getElementById('gender').value = data.gender || '';
            document.getElementById('birthDate').value = data.birth_date || '';
            document.getElementById('birthPlace').value = data.birth_place || '';
            document.getElementById('schoolYear').value = data.academic_year || '';
            document.getElementById('level').value = data.level || '';
            document.getElementById('class').value = data.class_name || '';
            document.getElementById('rt').value = data.serial_number || '';
            document.getElementById('phone1').value = data.phone1 || '';
            document.getElementById('phone2').value = data.phone2 || '';
            document.getElementById('notes').value = data.notes || '';
        }
        
        // دالة تحديث البيانات (للاستدعاء من Python)
        function updateStudentData(data) {
            loadStudentData(data);
            hideLoading();
        }
        
        function saveData() {
            const phone1 = document.getElementById('phone1').value;
            const phone2 = document.getElementById('phone2').value;
            const notes = document.getElementById('notes').value;
            
            if (bridge) {
                bridge.save_student_data(phone1, phone2, notes);
            }
        }
        
        function refreshData() {
            if (bridge) {
                bridge.refresh_data();
            }
        }
        
        function showMessage(type, message) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.className = `status-message ${type} show`;
            statusDiv.textContent = message;
            
            // إخفاء الرسالة بعد 3 ثوانٍ
            setTimeout(() => {
                statusDiv.classList.remove('show');
            }, 3000);
        }
        
        // تأثيرات التفاعل
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التركيز للحقول
            const inputs = document.querySelectorAll('.field-value, .notes-textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'scale(1.02)';
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>
        """
        
        self.web_view.setHtml(html_content)

    def send_data_to_web(self, data):
        """إرسال البيانات إلى واجهة الويب"""
        json_data = json.dumps(data, ensure_ascii=False)
        self.web_bridge.dataLoaded.emit(json_data)

    def connect_to_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            if self.external_db:
                self.db = self.external_db
                print("تم استخدام قاعدة البيانات الخارجية")
            else:
                self.db = QSqlDatabase.addDatabase("QSQLITE", "student_card_html_connection")
                self.db.setDatabaseName("data.db")

                if not self.db.open():
                    print(f"فشل في الاتصال بقاعدة البيانات: {self.db.lastError().text()}")
                    return False
                print("تم الاتصال بقاعدة البيانات المحلية")
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
            return False

    def load_current_academic_year(self):
        """تحميل السنة الدراسية الحالية"""
        try:
            if self.external_academic_year:
                self.current_academic_year = self.external_academic_year
                print(f"تم استخدام السنة الدراسية الخارجية: {self.current_academic_year}")
            else:
                # محاولة تحميل السنة الدراسية من قاعدة البيانات
                if hasattr(self, 'db') and self.db:
                    query = QSqlQuery(self.db)
                    query.exec_("SELECT academic_year FROM settings LIMIT 1")
                    if query.next():
                        self.current_academic_year = query.value(0)
                        print(f"تم تحميل السنة الدراسية: {self.current_academic_year}")
                    else:
                        # استخدام السنة الحالية كافتراضي
                        current_year = datetime.now().year
                        self.current_academic_year = f"{current_year}-{current_year + 1}"
                        print(f"تم استخدام السنة الدراسية الافتراضية: {self.current_academic_year}")
                else:
                    current_year = datetime.now().year
                    self.current_academic_year = f"{current_year}-{current_year + 1}"
                    print(f"تم استخدام السنة الدراسية الافتراضية: {self.current_academic_year}")
        except Exception as e:
            print(f"خطأ في تحميل السنة الدراسية: {e}")
            current_year = datetime.now().year
            self.current_academic_year = f"{current_year}-{current_year + 1}"

    def load_student_data(self):
        """تحميل بيانات الطالب وعرضها في HTML"""
        try:
            print(f"التشخيص load_student_data: current_record_id = {self.current_record_id}")
            print(f"التشخيص load_student_data: current_academic_year = {self.current_academic_year}")
            
            if not self.current_record_id:
                print("التشخيص: current_record_id فارغ - لن يتم تحميل البيانات")
                return

            # تحميل البيانات الأساسية
            if hasattr(self, 'db') and self.db:
                query = QSqlQuery(self.db)
                query.prepare("""
                    SELECT l.الرمز, s.الاسم_والنسب, s.النوع, s.تاريخ_الازدياد, s.مكان_الازدياد,
                           l.السنة_الدراسية, l.المستوى, l.القسم, l.رت, s.الهاتف_الأول, s.الهاتف_الثاني, s.ملاحظات
                    FROM اللوائح l
                    JOIN السجل_العام s ON l.الرمز = s.الرمز
                    WHERE l.الرمز = ? AND l.السنة_الدراسية = ?
                """)
                query.addBindValue(self.current_record_id)
                query.addBindValue(self.current_academic_year)
                
                print(f"التشخيص: تنفيذ الاستعلام للرمز: {self.current_record_id}, السنة: {self.current_academic_year}")

                if query.exec_():
                    print("التشخيص: تم تنفيذ الاستعلام بنجاح")
                    if query.next():
                        print("التشخيص: تم العثور على بيانات الطالب")
                        
                        # تجميع البيانات
                        student_data = {
                            'code': query.value(0) or "",
                            'name': query.value(1) or "",
                            'gender': query.value(2) or "",
                            'birth_date': query.value(3) or "",
                            'birth_place': query.value(4) or "",
                            'academic_year': query.value(5) or "",
                            'level': query.value(6) or "",
                            'class_name': query.value(7) or "",
                            'serial_number': query.value(8) or "",
                            'phone1': query.value(9) or "",
                            'phone2': query.value(10) or "",
                            'notes': query.value(11) or ""
                        }

                        print(f"التشخيص: تم تحديث البيانات - الاسم: {student_data['name']}")
                        
                        # تحديث البيانات في HTML
                        self.update_html_data(student_data)
                        
                    else:
                        print(f"التشخيص: لم يتم العثور على بيانات للطالب: {self.current_record_id}")
                        # إضافة استعلام تشخيصي للتحقق من وجود البيانات
                        debug_query = QSqlQuery(self.db)
                        debug_query.prepare("SELECT COUNT(*) FROM السجل_العام WHERE الرمز = ?")
                        debug_query.addBindValue(self.current_record_id)
                        if debug_query.exec_() and debug_query.next():
                            count = debug_query.value(0)
                            print(f"التشخيص: عدد السجلات للرمز {self.current_record_id}: {count}")
                        
                        # التحقق من السنة الدراسية
                        year_query = QSqlQuery(self.db)
                        year_query.prepare("SELECT DISTINCT l.السنة_الدراسية FROM اللوائح l WHERE l.الرمز = ?")
                        year_query.addBindValue(self.current_record_id)
                        if year_query.exec_():
                            years = []
                            while year_query.next():
                                years.append(year_query.value(0))
                            print(f"التشخيص: السنوات الدراسية المتاحة للرمز {self.current_record_id}: {years}")
                else:
                    print(f"التشخيص: فشل في تنفيذ الاستعلام: {query.lastError().text()}")
            else:
                print("التشخيص: قاعدة البيانات غير متوفرة")

        except Exception as e:
            print(f"خطأ في تحميل بيانات الطالب: {e}")
            import traceback
            traceback.print_exc()

    def update_html_data(self, data):
        """تحديث البيانات في HTML"""
        try:
            import json
            # طباعة البيانات للتشخيص
            print(f"تحديث البيانات: {data}")
            
            # إنشاء كود JavaScript مع التحقق من وجود الدوال
            js_code = f"""
                console.log('تحديث البيانات:', {json.dumps(data, ensure_ascii=False)});
                
                // التحقق من وجود دالة updateStudentData
                if (typeof updateStudentData === 'function') {{
                    updateStudentData({json.dumps(data, ensure_ascii=False)});
                    console.log('تم استدعاء updateStudentData');
                }} else {{
                    console.error('دالة updateStudentData غير موجودة');
                    // تحديث مباشر للحقول
                    if (document.getElementById('code')) document.getElementById('code').textContent = '{data.get('code', '')}';
                    if (document.getElementById('name')) document.getElementById('name').textContent = '{data.get('name', '')}';
                    if (document.getElementById('gender')) document.getElementById('gender').textContent = '{data.get('gender', '')}';
                    if (document.getElementById('birth_date')) document.getElementById('birth_date').textContent = '{data.get('birth_date', '')}';
                    if (document.getElementById('birth_place')) document.getElementById('birth_place').textContent = '{data.get('birth_place', '')}';
                    if (document.getElementById('academic_year')) document.getElementById('academic_year').textContent = '{data.get('academic_year', '')}';
                    if (document.getElementById('level')) document.getElementById('level').textContent = '{data.get('level', '')}';
                    if (document.getElementById('class_name')) document.getElementById('class_name').textContent = '{data.get('class_name', '')}';
                    if (document.getElementById('serial_number')) document.getElementById('serial_number').textContent = '{data.get('serial_number', '')}';
                    if (document.getElementById('phone1')) document.getElementById('phone1').value = '{data.get('phone1', '')}';
                    if (document.getElementById('phone2')) document.getElementById('phone2').value = '{data.get('phone2', '')}';
                    if (document.getElementById('notes')) document.getElementById('notes').value = '{data.get('notes', '')}';
                }}
            """
            
            # تنفيذ الكود مع callback للتحقق من النتيجة
            def callback_result(result):
                print(f"نتيجة تنفيذ JavaScript: {result}")
            
            self.web_view.page().runJavaScript(js_code, callback_result)
            print("تم إرسال البيانات إلى HTML")
        except Exception as e:
            print(f"خطأ في تحديث HTML: {e}")
            import traceback
            traceback.print_exc()

    def save_contact_info(self):
        """حفظ معلومات الاتصال والملاحظات"""
        try:
            if not self.current_record_id:
                self.show_message("تنبيه", "لا يوجد طالب محدد للحفظ")
                return

            # الحصول على البيانات من JavaScript
            js_code = """
                JSON.stringify({
                    phone1: document.getElementById('phone1').value,
                    phone2: document.getElementById('phone2').value,
                    notes: document.getElementById('notes').value
                });
            """
            
            def handle_data(result):
                try:
                    import json
                    contact_data = json.loads(result)
                    
                    if hasattr(self, 'db') and self.db:
                        query = QSqlQuery(self.db)
                        query.prepare("""
                            UPDATE السجل_العام
                            SET الهاتف_الأول = ?, الهاتف_الثاني = ?, ملاحظات = ?
                            WHERE الرمز = ?
                        """)
                        query.addBindValue(contact_data['phone1'])
                        query.addBindValue(contact_data['phone2'])
                        query.addBindValue(contact_data['notes'])
                        query.addBindValue(self.current_record_id)

                        if query.exec_():
                            self.show_message("نجح", "تم حفظ التغييرات بنجاح")
                        else:
                            self.show_message("خطأ", f"فشل في حفظ التغييرات: {query.lastError().text()}")
                    
                except Exception as e:
                    self.show_message("خطأ", f"خطأ في حفظ البيانات: {str(e)}")
            
            self.web_view.page().runJavaScript(js_code, handle_data)

        except Exception as e:
            self.show_message("خطأ", f"خطأ في حفظ البيانات: {str(e)}")

    def show_message(self, title, message):
        """عرض رسالة للمستخدم"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            if title == "نجح":
                QMessageBox.information(self, title, message)
            elif title == "خطأ":
                QMessageBox.critical(self, title, message)
            else:
                QMessageBox.warning(self, title, message)
        except:
            print(f"{title}: {message}")

    def show_student_data(self, student_code, academic_year=None):
        """عرض بيانات طالب محدد"""
        try:
            print(f"show_student_data: تم استدعاء عرض بيانات الطالب {student_code}")
            
            # تعيين رمز الطالب والسنة الدراسية
            self.current_record_id = student_code
            if academic_year:
                self.current_academic_year = academic_year
            
            # تحميل البيانات
            self.load_student_data()
            
            # إظهار النافذة في كامل الشاشة
            self.showMaximized()
            self.activateWindow()
            
        except Exception as e:
            print(f"خطأ في عرض بيانات الطالب: {e}")
            import traceback
            traceback.print_exc()

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        try:
            # إغلاق اتصال قاعدة البيانات إذا كان محلياً
            if hasattr(self, 'db') and self.db and not self.external_db:
                self.db.close()
                print("تم إغلاق اتصال قاعدة البيانات")
        except Exception as e:
            print(f"خطأ في إغلاق قاعدة البيانات: {e}")

        # قبول حدث الإغلاق
        event.accept()

    def load_first_student_for_testing(self):
        """تحميل أول طالب متوفر للاختبار"""
        try:
            if hasattr(self, 'db') and self.db:
                query = QSqlQuery(self.db)
                query.prepare("""
                    SELECT DISTINCT l.الرمز 
                    FROM اللوائح l
                    WHERE l.السنة_الدراسية = ?
                    ORDER BY l.الرمز ASC
                    LIMIT 1
                """)
                query.addBindValue(self.current_academic_year)
                
                if query.exec_() and query.next():
                    student_code = query.value(0)
                    print(f"تم العثور على أول طالب للاختبار: {student_code}")
                    self.current_record_id = student_code
                    
                    # تأخير تحميل البيانات قليلاً للتأكد من تحميل HTML
                    from PyQt5.QtCore import QTimer
                    QTimer.singleShot(1000, self.load_student_data)
                else:
                    print("لم يتم العثور على أي طلاب في قاعدة البيانات")
            else:
                print("قاعدة البيانات غير متوفرة")
        except Exception as e:
            print(f"خطأ في تحميل أول طالب: {e}")


def main():
    """الدالة الرئيسية لتشغيل النافذة"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # التحقق من وجود قاعدة البيانات
    if not check_database_availability():
        print("⚠️ تحذير: قاعدة البيانات غير متوفرة - سيتم تشغيل النافذة بوضع التجريب")
    
    # إنشاء النافذة
    window = StudentCardWindow()
    window.showMaximized()  # فتح في كامل الشاشة
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
