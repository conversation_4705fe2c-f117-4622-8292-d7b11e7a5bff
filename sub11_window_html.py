#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import json
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtSql import *
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

class StudentRecordsWindow(QMainWindow):
    """نافذة سجلات التلميذ - تستخدم منهجية Python + HTML الحديثة"""
    
    def __init__(self, student_code=None, student_name=None, db=None, parent=None):
        super().__init__(parent)
        
        # المتغيرات الأساسية
        self.student_code = student_code
        self.student_name = student_name or "غير محدد"
        self.db = db
        self.parent_window = parent
        
        # إعداد النافذة
        self.setupUI()
        
        # تحميل البيانات
        if self.student_code:
            self.load_student_records()
    
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        # إعدادات النافذة الأساسية
        self.setWindowTitle("")  # إزالة العنوان الرئيسي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # فتح النافذة في كامل الشاشة
        self.showMaximized()
        
        # إزالة قيود الحجم الثابت لتسمح بالعرض في كامل الشاشة
        # self.setFixedSize(1000, 700)  # تم تعطيل هذا السطر
        
        # إضافة أيقونة للنافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setLayoutDirection(Qt.RightToLeft)
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #1976d2;
                border-radius: 8px;
                background-color: white;
            }
            QTabBar::tab {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e3f2fd,
                    stop: 1 #bbdefb
                );
                color: #1976d2;
                padding: 0px;
                margin: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                font-size: 18px;
                min-width: 150px;
                height: 48px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #1976d2,
                    stop: 1 #1565c0
                );
                color: white;
            }
            QTabBar::tab:hover:!selected {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2196f3,
                    stop: 1 #1976d2
                );
                color: white;
            }
        """)
        
        # تبويب سجلات الدخول
        self.entry_tab = QWidget()
        self.entry_web_view = QWebEngineView()
        entry_layout = QVBoxLayout(self.entry_tab)
        entry_layout.setContentsMargins(5, 5, 5, 5)
        entry_layout.addWidget(self.entry_web_view)
        self.tab_widget.addTab(self.entry_tab, "🚪 سجلات الدخول")
        
        # تبويب سجلات التأخر
        self.late_tab = QWidget()
        self.late_web_view = QWebEngineView()
        late_layout = QVBoxLayout(self.late_tab)
        late_layout.setContentsMargins(5, 5, 5, 5)
        late_layout.addWidget(self.late_web_view)
        self.tab_widget.addTab(self.late_tab, "⏰ سجلات التأخر")
        
        main_layout.addWidget(self.tab_widget)
        
        # إضافة أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        buttons_layout.setContentsMargins(10, 5, 10, 10)
        
        # زر طباعة السجلات
        self.print_button = QPushButton("🖨️ طباعة السجلات")
        self.print_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.print_button.setMinimumHeight(40)
        self.print_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4CAF50,
                    stop: 1 #45a049
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5cbf60,
                    stop: 1 #4CAF50
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3d8b40,
                    stop: 1 #2e7d32
                );
            }
        """)
        self.print_button.clicked.connect(self.print_current_records)
        
        # زر حذف السجلات المحددة
        self.delete_button = QPushButton("🗑️ حذف السجلات المحددة")
        self.delete_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.delete_button.setMinimumHeight(40)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f44336,
                    stop: 1 #d32f2f
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f66356,
                    stop: 1 #f44336
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #c62828,
                    stop: 1 #b71c1c
                );
            }
        """)
        self.delete_button.clicked.connect(self.delete_selected_records)
        
        # زر تحديث البيانات
        self.refresh_button = QPushButton("🔄 تحديث البيانات")
        self.refresh_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.refresh_button.setMinimumHeight(40)
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2196F3,
                    stop: 1 #1976d2
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #42a5f5,
                    stop: 1 #2196F3
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #1565c0,
                    stop: 1 #0d47a1
                );
            }
        """)
        self.refresh_button.clicked.connect(self.load_student_records)
        
        # إضافة الأزرار للتخطيط
        buttons_layout.addWidget(self.print_button)
        buttons_layout.addWidget(self.delete_button)
        buttons_layout.addWidget(self.refresh_button)
        buttons_layout.addStretch()  # لجعل الأزرار تبدأ من اليمين
        
        # إضافة تخطيط الأزرار للتخطيط الرئيسي
        main_layout.addLayout(buttons_layout)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                border-top: 1px solid #ddd;
                font-size: 12px;
                color: #666;
            }
        """)
        self.status_bar.showMessage("جاهز...")
    
    def create_html_content(self, records, record_type):
        """إنشاء محتوى HTML للسجلات"""
        
        # تحديد العنوان والأيقونة حسب نوع السجل
        if record_type == "سماح":
            title = "سجلات الدخول"
            icon = "🚪"
            color = "#4CAF50"
        else:
            title = "سجلات التأخر"
            icon = "⏰"
            color = "#FF9800"
        
        # إنشاء صفوف الجدول
        table_rows = ""
        if records:
            for i, record in enumerate(records, 1):
                table_rows += f"""
                <tr class="record-row">
                    <td class="record-checkbox">
                        <input type="checkbox" class="record-select" 
                               data-paper-number="{record.get('رقم_الورقة', '')}"
                               data-date="{record.get('التاريخ', '')}"
                               data-time="{record.get('الوقت', '')}"
                               data-type="{record.get('ورقة_السماح', '')}">
                    </td>
                    <td class="record-number">{i}</td>
                    <td class="record-paper">{record.get('رقم_الورقة', 'غير محدد')}</td>
                    <td class="record-date">{record.get('التاريخ', 'غير محدد')}</td>
                    <td class="record-time">{record.get('الوقت', 'غير محدد')}</td>
                    <td class="record-type">{record.get('ورقة_السماح', 'غير محدد')}</td>
                </tr>
                """
        else:
            table_rows = f"""
            <tr>
                <td colspan="5" class="no-records">
                    <div class="empty-state">
                        <i class="empty-icon">{icon}</i>
                        <h3>لا توجد سجلات</h3>
                        <p>لم يتم العثور على أي سجلات {title.lower()} لهذا التلميذ</p>
                    </div>
                </td>
            </tr>
            """
        
        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{title}</title>
            <style>
                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}
                
                body {{
                    font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    padding: 20px;
                    direction: rtl;
                    color: #333;
                }}
                
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                    overflow: hidden;
                }}
                
                .header {{
                    background: linear-gradient(135deg, {color} 0%, {self.darken_color(color, 30)} 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                }}
                
                .header h1 {{
                    font-size: 2.5em;
                    margin-bottom: 10px;
                    font-weight: bold;
                }}
                
                .header .subtitle {{
                    font-size: 1.2em;
                    opacity: 0.9;
                }}
                
                .stats-bar {{
                    background: #f8f9fa;
                    padding: 20px;
                    border-bottom: 1px solid #e9ecef;
                    display: flex;
                    justify-content: space-around;
                    align-items: center;
                }}
                
                .stat-item {{
                    text-align: center;
                    padding: 15px;
                    background: white;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                    min-width: 150px;
                }}
                
                .stat-number {{
                    font-size: 2em;
                    font-weight: bold;
                    color: {color};
                    margin-bottom: 5px;
                }}
                
                .stat-label {{
                    color: #666;
                    font-size: 0.9em;
                }}
                
                .table-container {{
                    padding: 30px;
                }}
                
                .records-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                    background: white;
                    border-radius: 10px;
                    overflow: hidden;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                }}
                
                .records-table th {{
                    background: linear-gradient(135deg, {color} 0%, {self.darken_color(color, 20)} 100%);
                    color: white;
                    padding: 20px 15px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 17px;
                    border-bottom: 3px solid {self.darken_color(color, 40)};
                }}
                
                .records-table td {{
                    padding: 18px 15px;
                    text-align: center;
                    border-bottom: 1px solid #e9ecef;
                    font-size: 16px;
                    font-weight: bold;
                }}
                
                .record-row:nth-child(even) {{
                    background-color: #f8f9fa;
                }}
                
                .record-row:hover {{
                    background-color: {self.lighten_color(color, 90)};
                    transform: translateY(-2px);
                    transition: all 0.3s ease;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                }}
                
                .record-number {{
                    background: {color};
                    color: white;
                    border-radius: 50%;
                    width: 40px;
                    height: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin: 0 auto;
                    font-weight: bold;
                }}
                
                .record-paper {{
                    color: #2c3e50;
                    font-weight: bold;
                }}
                
                .record-date {{
                    color: #34495e;
                    font-family: 'Courier New', monospace;
                }}
                
                .record-time {{
                    color: #e74c3c;
                    font-family: 'Courier New', monospace;
                    font-weight: bold;
                }}
                
                .record-type {{
                    background: {self.lighten_color(color, 80)};
                    color: {self.darken_color(color, 30)};
                    padding: 8px 15px;
                    border-radius: 20px;
                    font-weight: bold;
                    display: inline-block;
                }}
                
                .no-records {{
                    padding: 60px 20px;
                    text-align: center;
                }}
                
                .empty-state {{
                    color: #666;
                }}
                
                .empty-icon {{
                    font-size: 4em;
                    margin-bottom: 20px;
                    display: block;
                    opacity: 0.5;
                }}
                
                .empty-state h3 {{
                    font-size: 1.5em;
                    margin-bottom: 10px;
                    color: #999;
                }}
                
                .empty-state p {{
                    font-size: 1.1em;
                    color: #bbb;
                }}
                
                @media (max-width: 768px) {{
                    .stats-bar {{
                        flex-direction: column;
                        gap: 15px;
                    }}
                    
                    .records-table {{
                        font-size: 14px;
                    }}
                    
                    .records-table th,
                    .records-table td {{
                        padding: 12px 8px;
                    }}
                }}
                
                /* أنماط خانات الاختيار */
                .record-checkbox {{
                    text-align: center;
                    width: 80px;
                }}
                
                .record-select {{
                    width: 18px;
                    height: 18px;
                    cursor: pointer;
                    transform: scale(1.2);
                }}
                
                #select-all {{
                    width: 18px;
                    height: 18px;
                    cursor: pointer;
                    transform: scale(1.2);
                    margin-left: 8px;
                }}
                
                .record-row.selected {{
                    background-color: {self.lighten_color(color, 95)} !important;
                    border-right: 4px solid {color};
                }}
            </style>
            
            <script>
                // وظيفة اختيار/إلغاء اختيار جميع السجلات
                function toggleSelectAll() {{
                    const selectAllBox = document.getElementById('select-all');
                    const checkboxes = document.querySelectorAll('.record-select');
                    
                    checkboxes.forEach(checkbox => {{
                        checkbox.checked = selectAllBox.checked;
                        updateRowSelection(checkbox);
                    }});
                }}
                
                // تحديث مظهر الصف عند الاختيار
                function updateRowSelection(checkbox) {{
                    const row = checkbox.closest('tr');
                    if (checkbox.checked) {{
                        row.classList.add('selected');
                    }} else {{
                        row.classList.remove('selected');
                    }}
                }}
                
                // إضافة مستمع للأحداث عند تحميل الصفحة
                document.addEventListener('DOMContentLoaded', function() {{
                    const checkboxes = document.querySelectorAll('.record-select');
                    checkboxes.forEach(checkbox => {{
                        checkbox.addEventListener('change', function() {{
                            updateRowSelection(this);
                            
                            // تحديث حالة "اختيار الكل"
                            const allCheckboxes = document.querySelectorAll('.record-select');
                            const checkedBoxes = document.querySelectorAll('.record-select:checked');
                            const selectAllBox = document.getElementById('select-all');
                            
                            if (checkedBoxes.length === 0) {{
                                selectAllBox.indeterminate = false;
                                selectAllBox.checked = false;
                            }} else if (checkedBoxes.length === allCheckboxes.length) {{
                                selectAllBox.indeterminate = false;
                                selectAllBox.checked = true;
                            }} else {{
                                selectAllBox.indeterminate = true;
                            }}
                        }});
                    }});
                }});
                
                // وظيفة للحصول على السجلات المحددة
                function getSelectedRecords() {{
                    const selectedCheckboxes = document.querySelectorAll('.record-select:checked');
                    const selectedRecords = [];
                    
                    selectedCheckboxes.forEach(checkbox => {{
                        selectedRecords.push({{
                            paperNumber: checkbox.getAttribute('data-paper-number'),
                            date: checkbox.getAttribute('data-date'),
                            time: checkbox.getAttribute('data-time'),
                            type: checkbox.getAttribute('data-type')
                        }});
                    }});
                    
                    return selectedRecords;
                }}
            </script>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>{icon} {title}</h1>
                    <div class="subtitle">التلميذ: {self.student_name} - الرمز: {self.student_code}</div>
                </div>
                
                <div class="stats-bar">
                    <div class="stat-item">
                        <div class="stat-number">{len(records) if records else 0}</div>
                        <div class="stat-label">إجمالي السجلات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{datetime.now().strftime('%Y-%m-%d')}</div>
                        <div class="stat-label">تاريخ الاستعلام</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{datetime.now().strftime('%H:%M')}</div>
                        <div class="stat-label">وقت الاستعلام</div>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="records-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all" onclick="toggleSelectAll()">
                                    اختيار الكل
                                </th>
                                <th>التسلسل</th>
                                <th>رقم الورقة</th>
                                <th>التاريخ</th>
                                <th>الوقت</th>
                                <th>نوع السجل</th>
                            </tr>
                        </thead>
                        <tbody>
                            {table_rows}
                        </tbody>
                    </table>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_content
    
    def darken_color(self, hex_color, amount):
        """تغميق اللون بمقدار محدد"""
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(max(0, c - amount) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"
    
    def lighten_color(self, hex_color, amount):
        """تفتيح اللون بمقدار محدد"""
        hex_color = hex_color.lstrip('#')
        rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        lightened = tuple(min(255, c + amount) for c in rgb)
        return f"#{lightened[0]:02x}{lightened[1]:02x}{lightened[2]:02x}"
    
    def load_student_records(self):
        """تحميل سجلات التلميذ من قاعدة البيانات"""
        try:
            self.status_bar.showMessage("جاري تحميل السجلات...")
            
            if not self.db or not self.student_code:
                self.status_bar.showMessage("خطأ: بيانات غير كاملة")
                return
            
            # استعلام للحصول على جميع السجلات
            query = QSqlQuery(self.db)
            query.prepare("""
                SELECT رقم_الورقة, التاريخ, الوقت, ورقة_السماح
                FROM ورقة_السماح_بالدخول
                WHERE الرمز = ?
                ORDER BY التاريخ DESC, الوقت DESC
            """)
            query.addBindValue(self.student_code)
            
            if not query.exec_():
                print(f"خطأ في تنفيذ الاستعلام: {query.lastError().text()}")
                self.status_bar.showMessage("خطأ في تحميل السجلات")
                return
            
            # تجميع السجلات
            all_records = []
            while query.next():
                record = {
                    'رقم_الورقة': query.value(0),
                    'التاريخ': query.value(1),
                    'الوقت': query.value(2),
                    'ورقة_السماح': query.value(3)
                }
                all_records.append(record)
            
            # تصنيف السجلات حسب النوع (إزالة المسافات الزائدة)
            entry_records = [r for r in all_records if str(r['ورقة_السماح']).strip() == 'سماح']
            late_records = [r for r in all_records if str(r['ورقة_السماح']).strip() == 'تأخر']
            
            # إنشاء محتوى HTML للتبويبات
            entry_html = self.create_html_content(entry_records, "سماح")
            late_html = self.create_html_content(late_records, "تأخر")
            
            # تحميل المحتوى في المتصفحات
            self.entry_web_view.setHtml(entry_html)
            self.late_web_view.setHtml(late_html)
            
            # تحديث شريط الحالة
            total_entry = len(entry_records)
            total_late = len(late_records)
            self.status_bar.showMessage(f"تم تحميل السجلات: {total_entry} دخول، {total_late} تأخر")
            
            print(f"تم تحميل {total_entry + total_late} سجل للتلميذ {self.student_code}")
            
        except Exception as e:
            print(f"خطأ في تحميل سجلات التلميذ: {e}")
            self.status_bar.showMessage(f"خطأ: {str(e)}")
    
    def delete_selected_records(self):
        """حذف السجلات المحددة من قاعدة البيانات"""
        try:
            # استخدام حوار تأكيد مخصص
            reply = QMessageBox.question(
                self, 
                "تأكيد الحذف", 
                "هل أنت متأكد من حذف السجلات المحددة؟\n\n⚠️ هذا الإجراء لا يمكن التراجع عنه!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # الحصول على التبويب النشط
            current_tab_index = self.tab_widget.currentIndex()
            if current_tab_index == 0:  # تبويب الدخول
                current_web_view = self.entry_web_view
                record_type = "سماح"
            else:  # تبويب التأخر
                current_web_view = self.late_web_view
                record_type = "تأخر"
            
            # تنفيذ جافا سكريبت للحصول على السجلات المحددة
            js_code = """
            (function() {
                const selectedRecords = getSelectedRecords();
                return JSON.stringify(selectedRecords);
            })();
            """
            
            def handle_selected_records(result):
                try:
                    if not result:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي سجلات للحذف!")
                        return
                    
                    selected_records = json.loads(result)
                    if not selected_records:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي سجلات للحذف!")
                        return
                    
                    # حذف السجلات من قاعدة البيانات
                    deleted_count = 0
                    query = QSqlQuery(self.db)
                    
                    for record in selected_records:
                        query.prepare("""
                            DELETE FROM ورقة_السماح_بالدخول 
                            WHERE الرمز = ? AND رقم_الورقة = ? AND التاريخ = ? AND الوقت = ?
                        """)
                        query.addBindValue(self.student_code)
                        query.addBindValue(record['paperNumber'])
                        query.addBindValue(record['date'])
                        query.addBindValue(record['time'])
                        
                        if query.exec_():
                            deleted_count += 1
                        else:
                            print(f"خطأ في حذف السجل: {query.lastError().text()}")
                    
                    # إظهار نتيجة الحذف
                    if deleted_count > 0:
                        QMessageBox.information(
                            self, 
                            "نجح الحذف", 
                            f"تم حذف {deleted_count} سجل بنجاح! ✅"
                        )
                        # إعادة تحميل البيانات
                        self.load_student_records()
                        self.status_bar.showMessage(f"تم حذف {deleted_count} سجل")
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف السجلات!")
                        
                except Exception as e:
                    print(f"خطأ في معالجة السجلات المحددة: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف:\n{str(e)}")
            
            # تنفيذ الجافا سكريبت والحصول على النتيجة
            current_web_view.page().runJavaScript(js_code, handle_selected_records)
            
        except Exception as e:
            print(f"خطأ في حذف السجلات: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف:\n{str(e)}")
    
    def print_current_records(self):
        """طباعة السجلات الحالية"""
        try:
            # الحصول على التبويب النشط
            current_tab_index = self.tab_widget.currentIndex()
            if current_tab_index == 0:  # تبويب الدخول
                current_web_view = self.entry_web_view
                tab_name = "سجلات الدخول"
            else:  # تبويب التأخر
                current_web_view = self.late_web_view
                tab_name = "سجلات التأخر"
            
            # حوار اختيار نوع الطباعة
            print_dialog = QMessageBox(self)
            print_dialog.setWindowTitle("خيارات الطباعة")
            print_dialog.setText(f"اختر نوع الطباعة لـ {tab_name}:")
            print_dialog.setIcon(QMessageBox.Question)
            
            # أزرار الخيارات
            print_all_btn = print_dialog.addButton("طباعة جميع السجلات 🖨️", QMessageBox.ActionRole)
            print_selected_btn = print_dialog.addButton("طباعة السجلات المحددة ✅", QMessageBox.ActionRole)
            export_pdf_btn = print_dialog.addButton("تصدير PDF 📄", QMessageBox.ActionRole)
            cancel_btn = print_dialog.addButton("إلغاء", QMessageBox.RejectRole)
            
            print_dialog.exec_()
            clicked_button = print_dialog.clickedButton()
            
            if clicked_button == cancel_btn:
                return
            elif clicked_button == print_all_btn:
                self._print_web_view(current_web_view, f"{tab_name} - {self.student_name}")
            elif clicked_button == print_selected_btn:
                self._print_selected_records(current_web_view, f"{tab_name} المحددة - {self.student_name}")
            elif clicked_button == export_pdf_btn:
                self._export_to_pdf(current_web_view, f"{tab_name}_{self.student_name}")
                
        except Exception as e:
            print(f"خطأ في الطباعة: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الطباعة:\n{str(e)}")
    
    def _print_web_view(self, web_view, title):
        """طباعة محتوى المتصفح"""
        try:
            # إنشاء حوار الطباعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setOrientation(QPrinter.Portrait)
            
            print_dialog = QPrintDialog(printer, self)
            print_dialog.setWindowTitle(f"طباعة {title}")
            
            if print_dialog.exec_() == QPrintDialog.Accepted:
                # طباعة المحتوى
                web_view.page().print(printer, lambda success: 
                    self.status_bar.showMessage("تمت الطباعة بنجاح ✅" if success else "فشلت الطباعة ❌")
                )
        except Exception as e:
            print(f"خطأ في طباعة المحتوى: {e}")
            QMessageBox.warning(self, "خطأ", f"فشل في الطباعة:\n{str(e)}")
    
    def _print_selected_records(self, web_view, title):
        """طباعة السجلات المحددة فقط"""
        try:
            # تنفيذ جافا سكريبت لإخفاء السجلات غير المحددة
            js_code = """
            (function() {
                const checkboxes = document.querySelectorAll('.record-select');
                const rows = document.querySelectorAll('.record-row');
                let hiddenCount = 0;
                
                checkboxes.forEach((checkbox, index) => {
                    if (!checkbox.checked) {
                        rows[index].style.display = 'none';
                        hiddenCount++;
                    }
                });
                
                // إخفاء عمود خانات الاختيار
                const checkboxCells = document.querySelectorAll('.record-checkbox, th:first-child');
                checkboxCells.forEach(cell => cell.style.display = 'none');
                
                return checkboxes.length - hiddenCount;
            })();
            """
            
            def handle_print_selected(visible_count):
                if visible_count == 0:
                    QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي سجلات للطباعة!")
                    return
                
                # طباعة المحتوى
                self._print_web_view(web_view, f"{title} ({visible_count} سجل)")
                
                # إعادة إظهار جميع الصفوف بعد الطباعة
                restore_js = """
                (function() {
                    const rows = document.querySelectorAll('.record-row');
                    const checkboxCells = document.querySelectorAll('.record-checkbox, th:first-child');
                    
                    rows.forEach(row => row.style.display = '');
                    checkboxCells.forEach(cell => cell.style.display = '');
                })();
                """
                web_view.page().runJavaScript(restore_js)
            
            web_view.page().runJavaScript(js_code, handle_print_selected)
            
        except Exception as e:
            print(f"خطأ في طباعة السجلات المحددة: {e}")
            QMessageBox.warning(self, "خطأ", f"فشل في طباعة السجلات المحددة:\n{str(e)}")
    
    def _export_to_pdf(self, web_view, filename):
        """تصدير المحتوى إلى ملف PDF"""
        try:
            # حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ تقرير PDF",
                f"{filename}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )
            
            if file_path:
                # إعداد الطابعة للتصدير
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setOrientation(QPrinter.Portrait)
                
                # تصدير المحتوى
                def export_callback(success):
                    if success:
                        self.status_bar.showMessage(f"تم تصدير PDF بنجاح: {file_path} ✅")
                        # سؤال المستخدم عن فتح الملف
                        reply = QMessageBox.question(
                            self, 
                            "تم التصدير", 
                            f"تم تصدير التقرير بنجاح!\n\nهل تريد فتح الملف؟\n{file_path}",
                            QMessageBox.Yes | QMessageBox.No
                        )
                        if reply == QMessageBox.Yes:
                            os.startfile(file_path)  # فتح الملف بالبرنامج الافتراضي
                    else:
                        self.status_bar.showMessage("فشل في تصدير PDF ❌")
                        QMessageBox.warning(self, "خطأ", "فشل في تصدير ملف PDF!")
                
                web_view.page().print(printer, export_callback)
                
        except Exception as e:
            print(f"خطأ في تصدير PDF: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير PDF:\n{str(e)}")

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        event.accept()


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة للاختبار
    window = StudentRecordsWindow(
        student_code="12345",
        student_name="أحمد محمد علي",
        db=None
    )
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
