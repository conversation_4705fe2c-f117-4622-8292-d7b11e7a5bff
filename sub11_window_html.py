#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import sqlite3
import json
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtSql import *
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

class UniversalStudentRecordsWindow(QMainWindow):
    """نافذة عرض سجلات جميع التلاميذ - تستخدم منهجية Python + HTML الحديثة"""
    
    def __init__(self, record_type="entry_permissions", parent=None):
        # إنشاء النافذة بدون parent لضمان فتحها في كامل الشاشة
        super().__init__(None)

        # تعيين خصائص النافذة لضمان فتحها في كامل الشاشة
        self.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)
        self.setAttribute(Qt.WA_DeleteOnClose, True)

        # المتغيرات الأساسية
        self.parent_window = parent
        self.record_type = record_type
        self.records_data = []
        self.filtered_data = []
        
        # إعدادات أنواع السجلات
        self.record_configs = {
            "entry_permissions": {
                "title": "🚪 سجل السماح بالدخول والتأخر",
                "table": "ورقة_السماح_بالدخول",
                "columns": [
                    ("rowid", "الرقم"),
                    ("التاريخ", "التاريخ"),
                    ("الرمز", "رمز التلميذ"),
                    ("الاسم_والنسب", "اسم التلميذ"),
                    ("الوقت", "الوقت"),
                    ("ورقة_السماح", "نوع السماح"),
                    ("السنة_الدراسية", "السنة الدراسية"),
                    ("الأسدس", "الأسدس"),
                    ("رقم_الورقة", "رقم الورقة")
                ],
                "filters": [
                    ("ورقة_السماح", "📋 نوع السماح")
                ],
                "stats": [
                    ("إجمالي السجلات", "len(data)"),
                    ("عدد التلاميذ", "len(set(row[3] for row in data if row[3]))"),
                    ("سماح دخول", "len([row for row in data if str(row[5]).strip() == 'سماح'])"),
                    ("تأخر", "len([row for row in data if str(row[5]).strip() == 'تأخر'])")
                ]
            },
            "absence_records": {
                "title": "📝 سجل الغياب الأسبوعي",
                "table": "مسك_الغياب_الأسبوعي",
                "columns": [
                    ("rowid", "الرقم"),
                    ("التاريخ", "التاريخ"),
                    ("رمز_التلميذ", "رمز التلميذ"),
                    ("اسم_التلميذ", "اسم التلميذ"),
                    ("القسم", "القسم"),
                    ("المستوى", "المستوى"),
                    ("غياب_غير_مبرر", "غياب غير مبرر"),
                    ("السنة_الدراسية", "السنة الدراسية"),
                    ("الأسدس", "الأسدس")
                ],
                "filters": [
                    ("القسم", "📚 القسم"),
                    ("المستوى", "📖 المستوى")
                ],
                "stats": [
                    ("إجمالي السجلات", "len(data)"),
                    ("عدد التلاميذ", "len(set(row[3] for row in data if row[3]))"),
                    ("الأقسام", "len(set(row[4] for row in data if row[4]))"),
                    ("إجمالي أيام الغياب", "sum(int(row[6] or 0) for row in data)")
                ]
            },
            "doctor_visits": {
                "title": "🏥 سجل زيارات الطبيب",
                "table": "زيارة_الطبيب",
                "columns": [
                    ("rowid", "الرقم"),
                    ("التاريخ", "التاريخ"),
                    ("الرمز", "رمز التلميذ"),
                    ("الاسم_والنسب", "اسم التلميذ"),
                    ("القسم", "القسم"),
                    ("المستوى", "المستوى"),
                    ("السبب", "سبب الزيارة"),
                    ("الملاحظات", "ملاحظات"),
                    ("الوقت", "الوقت")
                ],
                "filters": [
                    ("القسم", "📚 القسم"),
                    ("المستوى", "📖 المستوى"),
                    ("السبب", "🩺 سبب الزيارة")
                ],
                "stats": [
                    ("إجمالي الزيارات", "len(data)"),
                    ("عدد التلاميذ", "len(set(row[3] for row in data if row[3]))"),
                    ("الأقسام", "len(set(row[4] for row in data if row[4]))"),
                    ("اليوم", "len([row for row in data if row[1] == datetime.now().strftime('%Y-%m-%d')])")
                ]
            },
            "student_cards": {
                "title": "👤 بطاقات التلاميذ",
                "table": "تلاميذ",
                "columns": [
                    ("rowid", "الرقم"),
                    ("الرمز", "رمز التلميذ"),
                    ("الاسم_واللقب", "اسم التلميذ"),
                    ("تاريخ_الازدياد", "تاريخ الازدياد"),
                    ("مكان_الازدياد", "مكان الازدياد"),
                    ("القسم", "القسم"),
                    ("المستوى", "المستوى"),
                    ("الجنس", "الجنس"),
                    ("رقم_الهاتف", "رقم الهاتف"),
                    ("العنوان", "العنوان")
                ],
                "filters": [
                    ("القسم", "📚 القسم"),
                    ("المستوى", "📖 المستوى"),
                    ("الجنس", "👫 الجنس")
                ],
                "stats": [
                    ("إجمالي التلاميذ", "len(data)"),
                    ("ذكور", "len([row for row in data if str(row[7]).strip() == 'ذكر'])"),
                    ("إناث", "len([row for row in data if str(row[7]).strip() == 'أنثى'])"),
                    ("الأقسام", "len(set(row[5] for row in data if row[5]))")
                ]
            }
        }
        
        # الحصول على إعدادات السجل الحالي
        self.current_config = self.record_configs.get(record_type, self.record_configs["entry_permissions"])
        
        # إعداد النافذة
        self.setupUI()

        # تحميل البيانات
        self.load_records_data()

        # التأكد من فتح النافذة في كامل الشاشة
        self.showMaximized()

        # إضافة timer لضمان فتح النافذة في كامل الشاشة بعد التحميل الكامل
        QTimer.singleShot(100, self.ensure_maximized)

    def showEvent(self, event):
        """التأكد من فتح النافذة في كامل الشاشة عند عرضها"""
        super().showEvent(event)

        # ضمان فتح النافذة في كامل الشاشة
        self.setWindowState(Qt.WindowMaximized)
        self.showMaximized()

        # إضافة timer إضافي للتأكد
        QTimer.singleShot(50, lambda: self.setWindowState(Qt.WindowMaximized))
        QTimer.singleShot(100, self.showMaximized)

    def resizeEvent(self, event):
        """منع تصغير النافذة والحفاظ على كامل الشاشة"""
        super().resizeEvent(event)
        # يمكن إضافة منطق إضافي هنا إذا لزم الأمر
    
    def setupUI(self):
        """إعداد واجهة المستخدم"""
        # إعدادات النافذة الأساسية
        self.setWindowTitle(f"{self.current_config['title']} - عرض HTML حديث")
        self.setLayoutDirection(Qt.RightToLeft)

        # ضمان فتح النافذة في كامل الشاشة
        self.setWindowState(Qt.WindowMaximized)
        self.showMaximized()

        # إزالة أي قيود على الحجم
        self.setMinimumSize(0, 0)
        self.setMaximumSize(16777215, 16777215)
        
        # إضافة أيقونة للنافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # شريط الأدوات العلوي
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2196F3,
                    stop: 1 #1976d2
                );
                border-radius: 10px;
                padding: 10px;
            }
        """)

        # تحديد حد أقصى لارتفاع شريط الأدوات
        toolbar_frame.setMaximumHeight(200)
        toolbar_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # تغيير التخطيط إلى عمودي لتجنب مشكلة العرض الكبير
        toolbar_layout = QVBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)
        toolbar_layout.setSpacing(10)

        # الصف الأول: البحث ونوع السماح
        first_row_layout = QHBoxLayout()
        first_row_layout.setSpacing(15)
        
        # شريط البحث
        search_label = QLabel("🔍 البحث:")
        search_label.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
        first_row_layout.addWidget(search_label)

        self.search_entry = QLineEdit()
        self.search_entry.setPlaceholderText("ابحث في جميع الحقول...")
        self.search_entry.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid #1565c0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                min-width: 250px;
                max-width: 300px;
            }
            QLineEdit:focus {
                border-color: #0d47a1;
                box-shadow: 0 0 5px rgba(13, 71, 161, 0.5);
            }
        """)
        self.search_entry.textChanged.connect(self.on_search)
        first_row_layout.addWidget(self.search_entry)

        first_row_layout.addStretch()
        
        # إضافة فلتر نوع السماح إذا كان موجوداً في السجل الحالي
        self.filter_combos = {}
        if "ورقة_السماح" in [f[0] for f in self.current_config["filters"]]:
            # إنشاء تسمية فلتر نوع السماح
            permission_label = QLabel("📋 نوع السماح:")
            permission_label.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
            first_row_layout.addWidget(permission_label)
            
            # إنشاء مربع فلتر نوع السماح
            permission_combo = QComboBox()
            permission_combo.setStyleSheet("""
                QComboBox {
                    background: white;
                    border: 2px solid #1565c0;
                    border-radius: 8px;
                    padding: 8px 12px;
                    font-size: 14px;
                    min-width: 120px;
                    max-width: 150px;
                }
                QComboBox:focus {
                    border-color: #0d47a1;
                }
                QComboBox::drop-down {
                    border: none;
                    width: 20px;
                }
            """)
            permission_combo.currentTextChanged.connect(self.on_filter)
            first_row_layout.addWidget(permission_combo)
            
            # حفظ مرجع فلتر نوع السماح
            self.filter_combos["ورقة_السماح"] = permission_combo
        
        # زر تحديث البيانات
        self.refresh_button = QPushButton("🔄 تحديث")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4CAF50,
                    stop: 1 #45a049
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5cbf60,
                    stop: 1 #4CAF50
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3d8b40,
                    stop: 1 #2e7d32
                );
            }
        """)
        self.refresh_button.clicked.connect(self.load_records_data)
        first_row_layout.addWidget(self.refresh_button)
        
        # الصف الثاني: باقي الفلاتر (بدون نوع السماح)
        second_row_layout = QHBoxLayout()
        second_row_layout.setSpacing(15)

        # إنشاء باقي الفلاتر (استثناء نوع السماح)
        for filter_field, filter_label in self.current_config["filters"]:
            if filter_field != "ورقة_السماح":  # تجاهل نوع السماح لأنه في الصف الأول
                # إنشاء تسمية الفلتر
                label = QLabel(filter_label + ":")
                label.setStyleSheet("color: white; font-weight: bold; font-size: 14px;")
                second_row_layout.addWidget(label)

                # إنشاء مربع الفلتر
                combo = QComboBox()
                combo.setStyleSheet("""
                    QComboBox {
                        background: white;
                        border: 2px solid #1565c0;
                        border-radius: 8px;
                        padding: 8px 12px;
                        font-size: 14px;
                        min-width: 120px;
                        max-width: 150px;
                    }
                    QComboBox:focus {
                        border-color: #0d47a1;
                    }
                    QComboBox::drop-down {
                        border: none;
                        width: 20px;
                    }
                """)
                combo.currentTextChanged.connect(self.on_filter)
                second_row_layout.addWidget(combo)

                # حفظ مرجع الفلتر
                self.filter_combos[filter_field] = combo

        second_row_layout.addStretch()

        # إضافة الصفوف إلى التخطيط الرئيسي
        toolbar_layout.addLayout(first_row_layout)
        # إضافة الصف الثاني فقط إذا كان يحتوي على فلاتر
        if len([f for f in self.current_config["filters"] if f[0] != "ورقة_السماح"]) > 0:
            toolbar_layout.addLayout(second_row_layout)

        main_layout.addWidget(toolbar_frame)
        
        # منطقة عرض HTML
        self.web_view = QWebEngineView()
        self.web_view.setStyleSheet("""
            QWebEngineView {
                border: 2px solid #1976d2;
                border-radius: 10px;
                background: white;
            }
        """)
        main_layout.addWidget(self.web_view)
        
        # شريط الأزرار السفلي
        buttons_frame = QFrame()
        buttons_frame.setFrameStyle(QFrame.StyledPanel)
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #37474f,
                    stop: 1 #263238
                );
                border-radius: 10px;
                padding: 5px;
            }
        """)
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(15, 10, 15, 10)
        buttons_layout.setSpacing(15)
        
        # زر طباعة التقرير
        self.print_button = QPushButton("🖨️ طباعة التقرير")
        self.print_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.print_button.setMinimumHeight(40)
        self.print_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #FF9800,
                    stop: 1 #F57C00
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #FFB74D,
                    stop: 1 #FF9800
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #F57C00,
                    stop: 1 #E65100
                );
            }
        """)
        self.print_button.clicked.connect(self.print_report)
        buttons_layout.addWidget(self.print_button)
        
        # زر حذف السجلات المحددة
        self.delete_button = QPushButton("🗑️ حذف المحددة")
        self.delete_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.delete_button.setMinimumHeight(40)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f44336,
                    stop: 1 #d32f2f
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f66356,
                    stop: 1 #f44336
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #c62828,
                    stop: 1 #b71c1c
                );
            }
        """)
        self.delete_button.clicked.connect(self.delete_selected_records)
        buttons_layout.addWidget(self.delete_button)
        
        # زر تصدير PDF
        self.export_button = QPushButton("📄 تصدير PDF")
        self.export_button.setFont(QFont("Calibri", 12, QFont.Bold))
        self.export_button.setMinimumHeight(40)
        self.export_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #9C27B0,
                    stop: 1 #7B1FA2
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #BA68C8,
                    stop: 1 #9C27B0
                );
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #7B1FA2,
                    stop: 1 #4A148C
                );
            }
        """)
        self.export_button.clicked.connect(self.export_to_pdf)
        buttons_layout.addWidget(self.export_button)
        
        buttons_layout.addStretch()
        
        main_layout.addWidget(buttons_frame)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                border-top: 1px solid #ddd;
                font-size: 12px;
                color: #666;
                padding: 5px;
            }
        """)
        self.status_bar.showMessage("جاهز...")
    
    def load_records_data(self):
        """تحميل بيانات السجلات من قاعدة البيانات"""
        try:
            self.status_bar.showMessage("جاري تحميل البيانات...")
            
            conn = sqlite3.connect('data.db')
            cursor = conn.cursor()
            
            # بناء استعلام ديناميكي حسب نوع السجل
            table_name = self.current_config["table"]
            columns = [col[0] for col in self.current_config["columns"]]
            
            # التحقق من وجود الجدول أولاً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                print(f"الجدول {table_name} غير موجود")
                self.status_bar.showMessage(f"الجدول {table_name} غير موجود")
                QMessageBox.warning(self, "تنبيه", f"الجدول {table_name} غير موجود في قاعدة البيانات")
                conn.close()
                return
            
            # التحقق من الأعمدة الموجودة في الجدول
            cursor.execute(f"PRAGMA table_info({table_name})")
            existing_columns = [column[1] for column in cursor.fetchall()]
            print(f"الأعمدة الموجودة في جدول {table_name}: {existing_columns}")
            
            # تصفية الأعمدة الموجودة فقط
            valid_columns = []
            for col in columns:
                if col == "rowid" or col in existing_columns:
                    valid_columns.append(col)
                else:
                    print(f"العمود {col} غير موجود في الجدول {table_name}")
            
            if not valid_columns:
                print(f"لا توجد أعمدة صالحة في الجدول {table_name}")
                self.status_bar.showMessage("لا توجد أعمدة صالحة")
                conn.close()
                return
            
            # تحديث تكوين الأعمدة مع الأعمدة الصالحة فقط
            original_columns = self.current_config["columns"]
            self.current_config["columns"] = [
                (col[0], col[1]) for col in original_columns 
                if col[0] == "rowid" or col[0] in existing_columns
            ]
            
            # تحميل جميع السجلات مع ترتيب آمن
            try:
                # محاولة الترتيب حسب التاريخ إذا كان موجوداً
                if "التاريخ" in valid_columns:
                    query = f"SELECT {', '.join(valid_columns)} FROM {table_name} ORDER BY التاريخ DESC"
                else:
                    query = f"SELECT {', '.join(valid_columns)} FROM {table_name} ORDER BY rowid DESC"
                
                cursor.execute(query)
                self.records_data = cursor.fetchall()
                print(f"تم تحميل {len(self.records_data)} سجل من جدول {table_name}")
                
            except Exception as e:
                print(f"خطأ في استعلام البيانات: {e}")
                # محاولة استعلام بسيط بدون ترتيب
                query = f"SELECT {', '.join(valid_columns)} FROM {table_name}"
                cursor.execute(query)
                self.records_data = cursor.fetchall()
            
            # تحميل قوائم الفلاتر
            for filter_field, _ in self.current_config["filters"]:
                if filter_field in self.filter_combos and filter_field in existing_columns:
                    try:
                        cursor.execute(f'SELECT DISTINCT {filter_field} FROM {table_name} WHERE {filter_field} IS NOT NULL AND {filter_field} != ""')
                        values = [row[0] for row in cursor.fetchall()]
                        
                        combo = self.filter_combos[filter_field]
                        combo.clear()
                        combo.addItems(['الكل'] + sorted([str(v) for v in values if v]))
                    except Exception as e:
                        print(f"خطأ في تحميل فلتر {filter_field}: {e}")
            
            conn.close()
            
            # تطبيق الفلاتر وإنشاء HTML
            self.apply_filters()
            
            self.status_bar.showMessage(f"تم تحميل {len(self.records_data)} سجل")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.status_bar.showMessage(f"خطأ في تحميل البيانات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات:\n{str(e)}")
    
    def apply_filters(self):
        """تطبيق الفلاتر على البيانات"""
        search_term = self.search_entry.text().lower()
        
        self.filtered_data = []
        for row in self.records_data:
            # تطبيق فلاتر مربعات الاختيار
            skip_row = False
            for i, (filter_field, _) in enumerate(self.current_config["filters"]):
                if filter_field in self.filter_combos:
                    filter_value = self.filter_combos[filter_field].currentText()
                    if filter_value != 'الكل':
                        # العثور على فهرس العمود في البيانات
                        column_names = [col[0] for col in self.current_config["columns"]]
                        if filter_field in column_names:
                            column_index = column_names.index(filter_field)
                            if column_index < len(row) and str(row[column_index]) != filter_value:
                                skip_row = True
                                break
            
            if skip_row:
                continue
                
            # تطبيق البحث النصي
            if search_term:
                row_text = ' '.join(str(cell or '') for cell in row).lower()
                if search_term not in row_text:
                    continue
                    
            self.filtered_data.append(row)
        
        # إنشاء وعرض HTML
        self.generate_html()
    
    def on_search(self):
        """معالج البحث"""
        self.apply_filters()
    
    def on_filter(self):
        """معالج التصفية"""
        self.apply_filters()
    
    def generate_html(self):
        """توليد HTML لعرض السجلات"""
        data_to_display = self.filtered_data
        html_content = self.create_html_template(data_to_display)
        self.web_view.setHtml(html_content)
    
    def create_html_template(self, data):
        """إنشاء قالب HTML حديث"""
        # حساب الإحصائيات
        stats_html = ""
        for stat_name, stat_formula in self.current_config["stats"]:
            try:
                stat_value = eval(stat_formula)
                stats_html += f"""
                <div class="stat-card">
                    <div class="stat-number">{stat_value}</div>
                    <div>{stat_name}</div>
                </div>
                """
            except:
                stats_html += f"""
                <div class="stat-card">
                    <div class="stat-number">0</div>
                    <div>{stat_name}</div>
                </div>
                """
        
        # إنشاء رؤوس الجدول
        table_headers = """
        <th>
            <input type="checkbox" id="select-all" onclick="toggleSelectAll()">
            اختيار الكل
        </th>
        """
        
        for _, column_title in self.current_config["columns"]:
            table_headers += f"<th>{column_title}</th>"
        
        # إنشاء صفوف الجدول
        table_rows = ""
        for row in data:
            row_html = '<tr class="record-row">'
            
            # إضافة مربع الاختيار
            row_html += f"""
            <td class="record-checkbox">
                <input type="checkbox" class="record-select" 
                       data-id="{row[0] or ''}"
                       data-student-name="{row[3] if len(row) > 3 else ''}"
                       data-date="{row[1] if len(row) > 1 else ''}">
            </td>
            """
            
            # إضافة بيانات الصف
            for i, cell in enumerate(row):
                if i == 0:  # العمود الأول (الرقم)
                    row_html += f'<td><span class="record-id">{cell or "غير محدد"}</span></td>'
                elif "تاريخ" in self.current_config["columns"][i][1].lower():
                    row_html += f'<td class="date">{cell or "غير محدد"}</td>'
                elif "اسم" in self.current_config["columns"][i][1].lower():
                    row_html += f'<td class="student-name">{cell or "غير محدد"}</td>'
                elif "سماح" in self.current_config["columns"][i][1].lower():
                    cell_value = str(cell or "غير محدد").strip()
                    if cell_value == "سماح":
                        row_html += f'<td><span class="permission-tag entry">{cell_value}</span></td>'
                    elif cell_value == "تأخر":
                        row_html += f'<td><span class="permission-tag late">{cell_value}</span></td>'
                    else:
                        row_html += f'<td>{cell_value}</td>'
                else:
                    row_html += f'<td>{cell or "غير محدد"}</td>'
            
            row_html += '</tr>'
            table_rows += row_html
        
        # HTML الكامل
        html = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self.current_config['title']}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f8f9fa;
        }}
        
        .stat-card {{
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
        }}
        
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }}
        
        .table-container {{
            padding: 20px;
            overflow-x: auto;
        }}
        
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}
        
        th {{
            background: linear-gradient(45deg, #2c3e50, #34495e);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }}
        
        td {{
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #eee;
            transition: background-color 0.3s ease;
        }}
        
        tr:hover td {{
            background-color: #f8f9fa;
        }}
        
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        
        .record-id {{
            background: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
        }}
        
        .student-name {{
            font-weight: bold;
            color: #2c3e50;
        }}
        
        .date {{
            color: #7f8c8d;
            font-size: 0.9em;
        }}
        
        .permission-tag {{
            padding: 4px 12px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.9em;
        }}
        
        .permission-tag.entry {{
            background: #27ae60;
            color: white;
        }}
        
        .permission-tag.late {{
            background: #e74c3c;
            color: white;
        }}
        
        .record-checkbox {{
            text-align: center;
            width: 60px;
        }}
        
        .record-select {{
            width: 18px;
            height: 18px;
            cursor: pointer;
            transform: scale(1.2);
        }}
        
        #select-all {{
            width: 18px;
            height: 18px;
            cursor: pointer;
            transform: scale(1.2);
        }}
        
        .record-row.selected {{
            background-color: #e3f2fd !important;
            border-right: 4px solid #2196f3;
        }}
        
        .empty-state {{
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }}
        
        .empty-icon {{
            font-size: 4em;
            margin-bottom: 20px;
            display: block;
            opacity: 0.5;
        }}
    </style>
    
    <script>
        function toggleSelectAll() {{
            const selectAllBox = document.getElementById('select-all');
            const checkboxes = document.querySelectorAll('.record-select');
            
            checkboxes.forEach(checkbox => {{
                checkbox.checked = selectAllBox.checked;
                updateRowSelection(checkbox);
            }});
        }}
        
        function updateRowSelection(checkbox) {{
            const row = checkbox.closest('tr');
            if (checkbox.checked) {{
                row.classList.add('selected');
            }} else {{
                row.classList.remove('selected');
            }}
        }}
        
        function getSelectedRecords() {{
            const selectedCheckboxes = document.querySelectorAll('.record-select:checked');
            const selectedRecords = [];
            
            selectedCheckboxes.forEach(checkbox => {{
                selectedRecords.push({{
                    id: checkbox.getAttribute('data-id'),
                    studentName: checkbox.getAttribute('data-student-name'),
                    date: checkbox.getAttribute('data-date')
                }});
            }});
            
            return selectedRecords;
        }}
        
        document.addEventListener('DOMContentLoaded', function() {{
            const checkboxes = document.querySelectorAll('.record-select');
            checkboxes.forEach(checkbox => {{
                checkbox.addEventListener('change', function() {{
                    updateRowSelection(this);
                    
                    const allCheckboxes = document.querySelectorAll('.record-select');
                    const checkedBoxes = document.querySelectorAll('.record-select:checked');
                    const selectAllBox = document.getElementById('select-all');
                    
                    if (checkedBoxes.length === 0) {{
                        selectAllBox.indeterminate = false;
                        selectAllBox.checked = false;
                    }} else if (checkedBoxes.length === allCheckboxes.length) {{
                        selectAllBox.indeterminate = false;
                        selectAllBox.checked = true;
                    }} else {{
                        selectAllBox.indeterminate = true;
                    }}
                }});
            }});
        }});
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{self.current_config['title']}</h1>
            <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
        </div>
        
        <div class="stats">
            {stats_html}
        </div>
        
        <div class="table-container">
        """
        
        if data:
            html += f"""
            <table>
                <thead>
                    <tr>
                        {table_headers}
                    </tr>
                </thead>
                <tbody>
                    {table_rows}
                </tbody>
            </table>
            """
        else:
            html += """
            <div class="empty-state">
                <span class="empty-icon">📋</span>
                <h3>لا توجد سجلات</h3>
                <p>لم يتم العثور على أي سجلات تطابق معايير البحث</p>
            </div>
            """
        
        html += """
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def delete_selected_records(self):
        """حذف السجلات المحددة"""
        try:
            # تأكيد الحذف
            reply = QMessageBox.question(
                self, 
                "تأكيد الحذف", 
                "هل أنت متأكد من حذف السجلات المحددة؟\n\n⚠️ هذا الإجراء لا يمكن التراجع عنه!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # تنفيذ جافا سكريبت للحصول على السجلات المحددة
            js_code = """
            (function() {
                const selectedRecords = getSelectedRecords();
                return JSON.stringify(selectedRecords);
            })();
            """
            
            def handle_selected_records(result):
                try:
                    if not result:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي سجلات للحذف!")
                        return
                    
                    selected_records = json.loads(result)
                    if not selected_records:
                        QMessageBox.information(self, "تنبيه", "لم يتم تحديد أي سجلات للحذف!")
                        return
                    
                    # حذف السجلات من قاعدة البيانات
                    conn = sqlite3.connect('data.db')
                    cursor = conn.cursor()
                    deleted_count = 0
                    
                    for record in selected_records:
                        # استخدام rowid بدلاً من id
                        cursor.execute(
                            f"DELETE FROM {self.current_config['table']} WHERE rowid = ?",
                            (record['id'],)
                        )
                        if cursor.rowcount > 0:
                            deleted_count += 1
                    
                    conn.commit()
                    conn.close()
                    
                    # إظهار نتيجة الحذف
                    if deleted_count > 0:
                        QMessageBox.information(
                            self, 
                            "نجح الحذف", 
                            f"تم حذف {deleted_count} سجل بنجاح! ✅"
                        )
                        # إعادة تحميل البيانات
                        self.load_records_data()
                        self.status_bar.showMessage(f"تم حذف {deleted_count} سجل")
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في حذف السجلات!")
                        
                except Exception as e:
                    print(f"خطأ في معالجة السجلات المحددة: {e}")
                    QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف:\n{str(e)}")
            
            # تنفيذ الجافا سكريبت
            self.web_view.page().runJavaScript(js_code, handle_selected_records)
            
        except Exception as e:
            print(f"خطأ في حذف السجلات: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف:\n{str(e)}")
    
    def print_report(self):
        """طباعة التقرير"""
        try:
            # إنشاء حوار الطباعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setOrientation(QPrinter.Landscape)
            
            print_dialog = QPrintDialog(printer, self)
            print_dialog.setWindowTitle(f"طباعة {self.current_config['title']}")
            
            if print_dialog.exec_() == QPrintDialog.Accepted:
                # طباعة المحتوى
                self.web_view.page().print(printer, lambda success: 
                    self.status_bar.showMessage("تمت الطباعة بنجاح ✅" if success else "فشلت الطباعة ❌")
                )
        except Exception as e:
            print(f"خطأ في الطباعة: {e}")
            QMessageBox.warning(self, "خطأ", f"فشل في الطباعة:\n{str(e)}")
    
    def export_to_pdf(self):
        """تصدير التقرير إلى PDF"""
        try:
            # حوار حفظ الملف
            record_name = self.current_config['title'].replace('🚪 ', '').replace('📝 ', '').replace('🏥 ', '')
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                f"حفظ {record_name}",
                f"{record_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )
            
            if file_path:
                # إعداد الطابعة للتصدير
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)
                printer.setOrientation(QPrinter.Landscape)
                
                # تصدير المحتوى
                def export_callback(success):
                    if success:
                        self.status_bar.showMessage(f"تم تصدير PDF بنجاح ✅")
                        # سؤال المستخدم عن فتح الملف
                        reply = QMessageBox.question(
                            self, 
                            "تم التصدير", 
                            f"تم تصدير التقرير بنجاح!\n\nهل تريد فتح الملف؟",
                            QMessageBox.Yes | QMessageBox.No
                        )
                        if reply == QMessageBox.Yes:
                            os.startfile(file_path)
                    else:
                        self.status_bar.showMessage("فشل في تصدير PDF ❌")
                        QMessageBox.warning(self, "خطأ", "فشل في تصدير ملف PDF!")
                
                self.web_view.page().print(printer, export_callback)
                
        except Exception as e:
            print(f"خطأ في تصدير PDF: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير PDF:\n{str(e)}")
    
    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        # إيقاف مراقبة النافذة
        if hasattr(self, 'window_monitor_timer'):
            self.window_monitor_timer.stop()
        event.accept()

    def ensure_maximized(self):
        """دالة مساعدة لضمان فتح النافذة في كامل الشاشة"""
        # إزالة أي قيود على الحجم
        self.setMinimumSize(0, 0)
        self.setMaximumSize(16777215, 16777215)

        # تعيين حالة النافذة
        self.setWindowState(Qt.WindowMaximized)

        # إظهار النافذة في كامل الشاشة
        self.showMaximized()

        # تفعيل النافذة ورفعها للمقدمة
        self.activateWindow()
        self.raise_()

        # التأكد من التركيز
        self.setFocus()

        # بدء مراقبة حالة النافذة
        # بدء مراقبة حالة النافذة
        self.start_window_monitor()

    def start_window_monitor(self):
        """بدء مراقبة حالة النافذة للتأكد من بقائها في كامل الشاشة"""
        self.window_monitor_timer = QTimer()
        self.window_monitor_timer.timeout.connect(self.check_window_state)
        self.window_monitor_timer.start(1000)  # فحص كل ثانية

    def check_window_state(self):
        """فحص حالة النافذة والتأكد من أنها في كامل الشاشة"""
        if self.windowState() != Qt.WindowMaximized:
            self.setWindowState(Qt.WindowMaximized)
            self.showMaximized()


# للتوافق مع الكود القديم
class StudentRecordsWindow(UniversalStudentRecordsWindow):
    """نافذة سجلات التلميذ - للتوافق مع الكود القديم"""
    
    def __init__(self, student_code=None, student_name=None, db=None, parent=None):
        # تحويل للنافذة العامة مع فلتر التلميذ
        super().__init__("entry_permissions", parent)
        
        # إضافة فلتر للتلميذ المحدد إذا كان موجوداً
        if student_code:
            # يمكن إضافة منطق لفلترة البيانات حسب رمز التلميذ
            self.student_code = student_code
            self.student_name = student_name or "غير محدد"


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # إنشاء النافذة
    window = UniversalStudentRecordsWindow(record_type="entry_permissions")

    # ضمان فتح النافذة في كامل الشاشة بطرق متعددة
    window.setWindowState(Qt.WindowMaximized)
    window.showMaximized()
    window.ensure_maximized()

    # إضافة timer للتأكد من فتح النافذة في كامل الشاشة بعد بدء التطبيق
    QTimer.singleShot(200, window.ensure_maximized)
    QTimer.singleShot(500, window.ensure_maximized)

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
