#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sqlite3
import json
import base64
from datetime import datetime, timedelta
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

class AbsenceJustificationHTMLWindow(QMainWindow):
    """نافذة تبرير الغياب باستخدام منهجية Python + HTML الحديثة"""
    
    def __init__(self, db_path="data.db", parent=None):
        super().__init__(parent)
        self.db_path = db_path
        self.selected_image_path = None
        self.current_student = None
        
        # إعداد النافذة
        self.setupUI()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
        
        # إنشاء HTML وعرضه
        self.generate_html()

    def setupUI(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🏥 نافذة تبرير الغياب - عرض HTML حديث")
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين حجم النافذة
        screen = QApplication.desktop().screenGeometry()
        width = int(screen.width() * 0.9)
        height = int(screen.height() * 0.85)
        self.resize(width, height)
        
        # توسيط النافذة
        self.move(
            (screen.width() - width) // 2,
            (screen.height() - height) // 2
        )
        
        # إضافة أيقونة للنافذة
        try:
            self.setWindowIcon(QIcon("01.ico"))
        except:
            pass
        
        # تطبيق نمط احترافي للنافذة
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f0f4f8,
                    stop: 1 #e8f2f7
                );
            }
        """)
        
        # إنشاء الواجهة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # شريط الأدوات العلوي
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_frame.setMaximumHeight(80)
        toolbar_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #2196F3,
                    stop: 1 #1976d2
                );
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)
        toolbar_layout.setSpacing(15)
        
        # عنوان النافذة
        title_label = QLabel("🏥 تبرير الغياب")
        title_label.setFont(QFont("Calibri", 16, QFont.Bold))
        title_label.setStyleSheet("color: white;")
        toolbar_layout.addWidget(title_label)
        
        toolbar_layout.addStretch()
        
        # زر اختيار الصورة
        self.upload_button = QPushButton("📁 اختيار صورة")
        self.upload_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #16a085,
                    stop: 1 #138d75
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #1abc9c,
                    stop: 1 #16a085
                );
            }
        """)
        self.upload_button.clicked.connect(self.select_image)
        toolbar_layout.addWidget(self.upload_button)
        
        # زر حفظ التبرير
        self.save_button = QPushButton("💾 حفظ التبرير")
        self.save_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #27ae60,
                    stop: 1 #229954
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2ecc71,
                    stop: 1 #27ae60
                );
            }
        """)
        self.save_button.clicked.connect(self.save_justification)
        toolbar_layout.addWidget(self.save_button)
        
        # زر طباعة
        self.print_button = QPushButton("🖨️ طباعة")
        self.print_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e74c3c,
                    stop: 1 #c0392b
                );
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e67e22,
                    stop: 1 #e74c3c
                );
            }
        """)
        self.print_button.clicked.connect(self.print_report)
        toolbar_layout.addWidget(self.print_button)
        
        main_layout.addWidget(toolbar_frame)
        
        # منطقة عرض HTML
        self.web_view = QWebEngineView()
        self.web_view.setStyleSheet("""
            QWebEngineView {
                border: 2px solid #1976d2;
                border-radius: 10px;
                background: white;
            }
        """)
        main_layout.addWidget(self.web_view)
        
        # شريط الحالة
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                border-top: 1px solid #ddd;
                font-size: 12px;
                color: #666;
                padding: 5px;
            }
        """)
        self.status_bar.showMessage("جاهز...")

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # تحميل السنة الدراسية والأسدس
            cursor.execute("SELECT السنة_الدراسية, الأسدس FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            if result:
                self.school_year = str(result[0])
                self.semester = str(result[1])
            else:
                self.school_year = "2024/2025"
                self.semester = "الأول"
            
            conn.close()
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات الأولية: {e}")
            self.school_year = "2024/2025"
            self.semester = "الأول"

    def generate_html(self):
        """توليد HTML لعرض النموذج"""
        current_date = datetime.now().strftime('%Y-%m-%d')
        
        html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نافذة تبرير الغياب</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Calibri', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
            direction: rtl;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            font-family: 'Calibri', sans-serif;
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        
        .header p {{
            font-family: 'Calibri', sans-serif;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        
        .main-content {{
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            padding: 30px;
        }}
        
        .form-section {{
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }}
        
        .section-title {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1e3a8a;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }}
        
        .form-grid {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }}
        
        .form-row {{
            display: flex;
            flex-direction: column;
            gap: 5px;
        }}
        
        .form-row.full-width {{
            grid-column: 1 / -1;
        }}
        
        label {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1e3a8a;
        }}
        
        h3 {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1e3a8a;
            margin: 20px 0 15px;
        }}
        
        input[type="text"], input[type="date"], input[type="number"], 
        textarea, select {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1f2937;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            transition: all 0.3s ease;
            background: white;
        }}
        
        input[type="text"]:focus, input[type="date"]:focus, 
        input[type="number"]:focus, textarea:focus, select:focus {{
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }}
        
        input[readonly] {{
            background-color: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }}
        
        textarea {{
            resize: vertical;
            min-height: 80px;
        }}
        
        .image-section {{
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e9ecef;
            text-align: center;
        }}
        
        .image-preview {{
            width: 100%;
            max-height: 300px;
            border: 2px dashed #bdc3c7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            background: #fdfdfd;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px;
        }}
        
        .image-preview img {{
            max-width: 100%;
            max-height: 100%;
            border-radius: 5px;
        }}
        
        .placeholder-text {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            color: #7f8c8d;
        }}
        
        .instructions {{
            background: #e8f4fd;
            padding: 20px;
            border-radius: 10px;
            border-right: 4px solid #3498db;
            margin-top: 20px;
        }}
        
        .instructions h3 {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1e3a8a;
            margin-bottom: 10px;
        }}
        
        .instructions ul {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1f2937;
            line-height: 1.6;
        }}
        
        .instructions li {{
            margin-bottom: 5px;
        }}
        
        .button-group {{
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }}
        
        .btn {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }}
        
        .btn-success {{
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
        }}
        
        .btn-success:hover {{
            background: linear-gradient(45deg, #219a52, #27ae60);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }}
        
        .alert {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: none;
        }}
        
        .alert-success {{
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }}
        
        .alert-danger {{
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }}
        
        .loading {{
            font-family: 'Calibri', sans-serif;
            font-size: 17px;
            font-weight: bold;
            color: #1f2937;
            display: none;
            text-align: center;
            padding: 20px;
        }}
        
        .spinner {{
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }}
        
        @keyframes spin {{
            0% {{ transform: rotate(0deg); }}
            100% {{ transform: rotate(360deg); }}
        }}
        
        @media (max-width: 768px) {{
            .main-content {{
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 20px;
            }}
            
            .form-grid {{
                grid-template-columns: 1fr;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏥 تبرير الغياب</h1>
            <p>نموذج تبرير الغياب للتلاميذ</p>
        </div>
        
        <div class="main-content">
            <div class="form-section">
                <h2 class="section-title">📋 معلومات التبرير</h2>
                
                <div id="alerts"></div>
                
                <!-- معلومات التلميذ -->
                <div class="form-grid">
                    <div class="form-row">
                        <label>رمز التلميذ:</label>
                        <input type="text" id="student_code" readonly placeholder="غير محدد">
                    </div>
                    <div class="form-row">
                        <label>الاسم والنسب:</label>
                        <input type="text" id="student_name" readonly placeholder="غير محدد">
                    </div>
                    <div class="form-row">
                        <label>الرقم الترتيبي:</label>
                        <input type="text" id="student_id" readonly placeholder="غير محدد">
                    </div>
                    <div class="form-row">
                        <label>المستوى:</label>
                        <input type="text" id="level" readonly placeholder="غير محدد">
                    </div>
                    <div class="form-row">
                        <label>القسم:</label>
                        <input type="text" id="class_name" readonly placeholder="غير محدد">
                    </div>
                    <div class="form-row">
                        <label>السنة الدراسية:</label>
                        <input type="text" id="school_year" value="{self.school_year}" readonly>
                    </div>
                    <div class="form-row">
                        <label>الأسدس:</label>
                        <input type="text" id="semester" value="{self.semester}" readonly>
                    </div>
                </div>
                
                <!-- معلومات التبرير -->
                <h3>📅 تفاصيل الغياب</h3>
                <div class="form-grid">
                    <div class="form-row">
                        <label>تاريخ التبرير:</label>
                        <input type="date" id="justification_date" value="{current_date}">
                    </div>
                    <div class="form-row">
                        <label>تاريخ بداية الغياب:</label>
                        <input type="date" id="start_date" value="{current_date}" onchange="calculateEndDate()">
                    </div>
                    <div class="form-row">
                        <label>تاريخ نهاية الغياب:</label>
                        <input type="date" id="end_date" value="{current_date}">
                    </div>
                    <div class="form-row">
                        <label>عدد الأيام:</label>
                        <input type="number" id="days_count" value="1" min="1" max="365" onchange="calculateEndDate()">
                    </div>
                    <div class="form-row full-width">
                        <label>سبب الغياب:</label>
                        <input type="text" id="reason" value="شهادة طبية" placeholder="أدخل سبب الغياب">
                    </div>
                    <div class="form-row full-width">
                        <label>ملاحظات إضافية:</label>
                        <textarea id="notes" placeholder="يمكنك إضافة ملاحظات إضافية هنا..."></textarea>
                    </div>
                </div>
                
                <div class="button-group">
                    <button class="btn btn-success" onclick="saveJustification()">
                        💾 حفظ التبرير
                    </button>
                </div>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>جاري حفظ البيانات...</p>
                </div>
            </div>
            
            <div class="image-section">
                <h2 class="section-title">📸 صورة التبرير</h2>
                
                <div class="image-preview" id="image-preview">
                    <div class="placeholder-text">لم يتم اختيار أي صورة</div>
                </div>
                
                <div class="instructions">
                    <h3>تعليمات:</h3>
                    <ul>
                        <li>تأكد من اختيار التلميذ الصحيح قبل حفظ التبرير</li>
                        <li>يمكنك إضافة صورة للشهادة الطبية أو مبرر الغياب (اختياري)</li>
                        <li>تحديد عدد الأيام سيقوم بحساب تاريخ النهاية تلقائياً</li>
                        <li>تأكد من صحة البيانات قبل الحفظ</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function calculateEndDate() {{
            const startDate = document.getElementById('start_date').value;
            const daysCount = parseInt(document.getElementById('days_count').value) || 1;
            
            if (startDate) {{
                const start = new Date(startDate);
                const end = new Date(start);
                end.setDate(start.getDate() + daysCount - 1);
                
                const endDateStr = end.toISOString().split('T')[0];
                document.getElementById('end_date').value = endDateStr;
            }}
        }}
        
        function showAlert(message, type) {{
            const alertsContainer = document.getElementById('alerts');
            const alert = document.createElement('div');
            alert.className = `alert alert-${{type}}`;
            alert.textContent = message;
            alert.style.display = 'block';
            
            alertsContainer.innerHTML = '';
            alertsContainer.appendChild(alert);
            
            setTimeout(() => {{
                alert.style.display = 'none';
            }}, 5000);
        }}
        
        function validateForm() {{
            const studentCode = document.getElementById('student_code').value.trim();
            const studentName = document.getElementById('student_name').value.trim();
            const reason = document.getElementById('reason').value.trim();
            
            if (!studentCode || !studentName) {{
                showAlert('الرجاء اختيار تلميذ أولاً', 'danger');
                return false;
            }}
            
            if (!reason) {{
                showAlert('الرجاء إدخال سبب الغياب', 'danger');
                return false;
            }}
            
            return true;
        }}
        
        function saveJustification() {{
            if (!validateForm()) {{
                return;
            }}
            
            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            
            // جمع البيانات
            const data = {{
                student_code: document.getElementById('student_code').value,
                student_name: document.getElementById('student_name').value,
                student_id: document.getElementById('student_id').value,
                level: document.getElementById('level').value,
                class_name: document.getElementById('class_name').value,
                school_year: document.getElementById('school_year').value,
                semester: document.getElementById('semester').value,
                justification_date: document.getElementById('justification_date').value,
                start_date: document.getElementById('start_date').value,
                end_date: document.getElementById('end_date').value,
                days_count: document.getElementById('days_count').value,
                reason: document.getElementById('reason').value,
                notes: document.getElementById('notes').value
            }};
            
            // إرسال البيانات إلى Python
            window.bridge.saveJustification(JSON.stringify(data));
        }}
        
        function setStudentInfo(code, name, id, level, className) {{
            try {{
                console.log('🔄 بدء تعيين معلومات التلميذ:', {{code, name, id, level, className}});

                const elements = {{
                    'student_code': code || '',
                    'student_name': name || '',
                    'student_id': id || '',
                    'level': level || '',
                    'class_name': className || ''
                }};

                for (const [elementId, value] of Object.entries(elements)) {{
                    const element = document.getElementById(elementId);
                    if (element) {{
                        element.value = value;
                        console.log(`✅ تم تعيين ${{elementId}}: ${{value}}`);
                    }} else {{
                        console.error(`❌ لم يتم العثور على العنصر: ${{elementId}}`);
                    }}
                }}

                console.log('✅ تم تعيين جميع معلومات التلميذ بنجاح');

                // إظهار رسالة نجاح
                showAlert(`تم تحديد التلميذ: ${{name}}`, 'success');

            }} catch(error) {{
                console.error('❌ خطأ في تعيين معلومات التلميذ:', error);
                showAlert('حدث خطأ في تحديد معلومات التلميذ', 'danger');
            }}
        }}
        
        function setImage(imagePath) {{
            const preview = document.getElementById('image-preview');
            if (imagePath) {{
                preview.innerHTML = `<img src="file:///${{imagePath}}" alt="صورة التبرير">`;
            }} else {{
                preview.innerHTML = '<div class="placeholder-text">لم يتم اختيار أي صورة</div>';
            }}
        }}
        
        function clearForm() {{
            document.getElementById('notes').value = '';
            document.getElementById('days_count').value = '1';
            document.getElementById('reason').value = 'شهادة طبية';
            setImage(null);
            calculateEndDate();
        }}
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {{
            calculateEndDate();
        }});
    </script>
</body>
</html>
        """
        
        self.web_view.setHtml(html_content)

    def select_image(self):
        """اختيار صورة من الملفات"""
        try:
            options = QFileDialog.Options()
            file_path, _ = QFileDialog.getOpenFileName(
                self, "اختيار صورة", "",
                "صور (*.png *.jpg *.jpeg *.bmp *.gif);;كل الملفات (*)",
                options=options
            )
            
            if file_path:
                self.selected_image_path = file_path
                # تحديث الصورة في HTML
                js_code = f"setImage('{file_path.replace(chr(92), '/')}')"
                self.web_view.page().runJavaScript(js_code)
                self.status_bar.showMessage(f"تم اختيار الصورة: {os.path.basename(file_path)}")
            
        except Exception as e:
            print(f"خطأ في اختيار الصورة: {e}")
            QMessageBox.warning(self, "خطأ", f"فشل في اختيار الصورة:\n{str(e)}")

    def save_justification(self):
        """حفظ تبرير الغياب"""
        # سيتم استدعاء هذه الدالة من JavaScript
        js_code = "saveJustification()"
        self.web_view.page().runJavaScript(js_code)

    def handle_save_justification(self, data_json):
        """معالجة حفظ تبرير الغياب من JavaScript"""
        try:
            data = json.loads(data_json)
            
            # التحقق من البيانات
            if not data.get('student_code') or not data.get('student_name'):
                js_code = "showAlert('الرجاء اختيار تلميذ أولاً', 'danger')"
                self.web_view.page().runJavaScript(js_code)
                return
            
            # إنشاء اتصال قاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # إنشاء جدول تبريرات الغياب إذا لم يكن موجوداً
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS تبريرات_الغياب (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    رمز_التلميذ TEXT,
                    اسم_التلميذ TEXT,
                    ر_ت TEXT,
                    المستوى TEXT,
                    القسم TEXT,
                    تاريخ_التبرير TEXT,
                    تاريخ_البداية TEXT,
                    تاريخ_النهاية TEXT,
                    عدد_الأيام INTEGER,
                    عدد_الساعات INTEGER,
                    سبب_الغياب TEXT,
                    ملاحظات TEXT,
                    مسار_الصورة TEXT,
                    تاريخ_التسجيل TEXT,
                    السنة_الدراسية TEXT,
                    الأسدس TEXT
                )
            ''')
            
            # معالجة الصورة إذا كانت موجودة
            image_path = None
            if self.selected_image_path:
                images_dir = os.path.join(os.path.dirname(self.db_path), "absence_images")
                if not os.path.exists(images_dir):
                    os.makedirs(images_dir)
                
                file_ext = os.path.splitext(self.selected_image_path)[1]
                new_file_name = f"{data['student_code']}_{datetime.now().strftime('%Y%m%d%H%M%S')}{file_ext}"
                new_file_path = os.path.join(images_dir, new_file_name)
                
                import shutil
                shutil.copy2(self.selected_image_path, new_file_path)
                image_path = new_file_path
            
            # إدخال البيانات
            cursor.execute('''
                INSERT INTO تبريرات_الغياب (
                    رمز_التلميذ, اسم_التلميذ, ر_ت, المستوى, القسم,
                    تاريخ_التبرير, تاريخ_البداية, تاريخ_النهاية,
                    عدد_الأيام, عدد_الساعات, سبب_الغياب, ملاحظات,
                    مسار_الصورة, تاريخ_التسجيل, السنة_الدراسية, الأسدس
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                data['student_code'], data['student_name'], data['student_id'],
                data['level'], data['class_name'], data['justification_date'],
                data['start_date'], data['end_date'], int(data['days_count']),
                0, data['reason'], data['notes'], image_path,
                datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                data['school_year'], data['semester']
            ))
            
            conn.commit()
            conn.close()
            
            # إظهار رسالة نجاح
            success_message = f"تم حفظ تبرير الغياب بنجاح للتلميذ {data['student_name']}"
            js_code = f"showAlert('{success_message}', 'success'); clearForm();"
            self.web_view.page().runJavaScript(js_code)
            
            self.status_bar.showMessage("تم حفظ تبرير الغياب بنجاح")
            
            # مسح الصورة المحددة
            self.selected_image_path = None
            
        except Exception as e:
            print(f"خطأ في حفظ تبرير الغياب: {e}")
            js_code = f"showAlert('حدث خطأ أثناء حفظ البيانات: {str(e)}', 'danger')"
            self.web_view.page().runJavaScript(js_code)
            
        finally:
            # إخفاء شاشة التحميل
            js_code = "document.getElementById('loading').style.display = 'none';"
            self.web_view.page().runJavaScript(js_code)

    def set_student_info(self, code, name, id_num, level, class_name):
        """تعيين معلومات التلميذ"""
        try:
            print(f"🔍 محاولة تعيين معلومات التلميذ: {code} - {name}")

            # التحقق من وجود التلميذ في جدول اللوائح أولاً
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # البحث في جدول اللوائح للسنة الحالية
            cursor.execute("""
                SELECT COUNT(*) FROM اللوائح
                WHERE الرمز = ? AND السنة_الدراسية = ?
            """, (code, self.school_year))

            exists_in_lists = cursor.fetchone()[0] > 0

            # إذا لم يوجد في اللوائح، ابحث في السجل العام
            if not exists_in_lists:
                cursor.execute("""
                    SELECT COUNT(*) FROM السجل_العام
                    WHERE الرمز = ?
                """, (code,))

                exists_in_general = cursor.fetchone()[0] > 0

                if not exists_in_general:
                    print(f"⚠️ التلميذ {name} غير موجود في قاعدة البيانات")
                    conn.close()
                    # لا نعرض رسالة تحذير، فقط نسجل في الكونسول
                    # QMessageBox.warning(
                    #     self, "تنبيه",
                    #     f"التلميذ {name} غير موجود في قاعدة البيانات"
                    # )
                    # return False
                else:
                    print(f"ℹ️ التلميذ {name} موجود في السجل العام لكن ليس في اللوائح للسنة الحالية")
            else:
                print(f"✅ التلميذ {name} موجود في اللوائح للسنة الحالية")

            conn.close()

            # تحديث معلومات التلميذ في HTML
            js_code = f"""
                try {{
                    setStudentInfo('{code}', '{name}', '{id_num}', '{level}', '{class_name}');
                    console.log('✅ تم تعيين معلومات التلميذ بنجاح في HTML');
                }} catch(e) {{
                    console.error('❌ خطأ في تعيين معلومات التلميذ في HTML:', e);
                }}
            """
            self.web_view.page().runJavaScript(js_code)

            self.current_student = {
                'code': code,
                'name': name,
                'id': id_num,
                'level': level,
                'class': class_name
            }

            self.status_bar.showMessage(f"تم تحديد التلميذ: {name}")
            print(f"✅ تم تعيين معلومات التلميذ بنجاح: {name}")
            return True

        except Exception as e:
            print(f"❌ خطأ في تعيين معلومات التلميذ: {e}")
            import traceback
            traceback.print_exc()
            return False

    def print_report(self):
        """طباعة التقرير"""
        try:
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            printer.setOrientation(QPrinter.Portrait)
            
            print_dialog = QPrintDialog(printer, self)
            print_dialog.setWindowTitle("طباعة تبرير الغياب")
            
            if print_dialog.exec_() == QPrintDialog.Accepted:
                self.web_view.page().print(printer, lambda success: 
                    self.status_bar.showMessage("تمت الطباعة بنجاح ✅" if success else "فشلت الطباعة ❌")
                )
        except Exception as e:
            print(f"خطأ في الطباعة: {e}")
            QMessageBox.warning(self, "خطأ", f"فشل في الطباعة:\n{str(e)}")

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        event.accept()


class AbsenceJustificationWidget(QWidget):
    """ويدجت يمكن تضمينه في واجهات أخرى"""
    
    def __init__(self, db_path="data.db", parent=None):
        super().__init__(parent)
        self.db_path = db_path
        
        # إنشاء التخطيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء النافذة كعنصر داخلي
        self.absence_window = AbsenceJustificationHTMLWindow(db_path, parent=self)
        
        # إضافة الويدجت المركزي
        central_widget = self.absence_window.centralWidget()
        layout.addWidget(central_widget)
    
    def set_student_info(self, code, name, id_num, level, class_name):
        """إعادة توجيه تعيين معلومات التلميذ"""
        return self.absence_window.set_student_info(code, name, id_num, level, class_name)


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    import sys
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = AbsenceJustificationHTMLWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
